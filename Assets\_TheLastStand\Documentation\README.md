# TheLastStand - Project Documentation

Welcome to the comprehensive documentation for **TheLastStand**, a multiplayer survival game built with Unity and Steam integration.

## 📁 Documentation Structure

### 🏗️ [Architecture](./Architecture/)

- [System Overview](./Architecture/SystemOverview.md) - High-level architecture and system relationships
- [Networking Architecture](./Architecture/NetworkingArchitecture.md) - Mirror networking and Steam integration
- [Scene Management](./Architecture/SceneManagement.md) - Scene flow and transitions
- [Player Management](./Architecture/PlayerManagement.md) - Player spawning and lifecycle
- [Helicopter Constraint System](./Architecture/HelicopterConstraintSystem.md) - Position and rotation constraints during flight
- [Helicopter Immobilization System](./Architecture/HelicopterImmobilizationSystem.md) - Complete player immobilization during helicopter sequences

### 📚 [API Reference](./API/)

- [Core Systems](./API/CoreSystems.md) - MyNetworkManager, GameManager, etc.
- [Player Systems](./API/PlayerSystems.md) - MyClient, PlayerIdentity, etc.
- [Networking](./API/Networking.md) - LobbyController, SteamLobby, etc.
- [UI Systems](./API/UISystems.md) - MainMenu, PopupManager, etc.

### 🎮 [Game Systems](./GameSystems/)

- [Forest Map](./GameSystems/ForestMap.md) - Forest intro sequence and gameplay
- [Main Menu](./GameSystems/MainMenu.md) - Menu systems and lobby management
- [Audio System](./GameSystems/AudioSystem.md) - Audio management and settings
- [Steam Integration](./GameSystems/SteamIntegration.md) - Steam features and lobby system

### 🛠️ [Development](./Development/)

- [Setup Guide](./Development/SetupGuide.md) - Project setup and requirements
- [Coding Standards](./Development/CodingStandards.md) - Code style and conventions
- [Workflows](./Development/Workflows.md) - Development processes and best practices
- [Testing Guide](./Development/TestingGuide.md) - Testing strategies and tools

### 🎨 [Assets](./Assets/)

- [Scene Documentation](./Assets/Scenes.md) - Scene structure and components
- [Prefab Documentation](./Assets/Prefabs.md) - Prefab organization and usage
- [Audio Assets](./Assets/Audio.md) - Audio file organization and implementation

### 🔧 [Troubleshooting](./Troubleshooting/)

- [Common Issues](./Troubleshooting/CommonIssues.md) - Frequently encountered problems
- [Networking Issues](./Troubleshooting/NetworkingIssues.md) - Network-related problems
- [Steam Issues](./Troubleshooting/SteamIssues.md) - Steam integration problems
- [Performance Issues](./Troubleshooting/PerformanceIssues.md) - Performance optimization

### 📋 [Templates](./Templates/)

- [System Documentation Template](./Templates/SystemDocumentationTemplate.md)
- [API Documentation Template](./Templates/APIDocumentationTemplate.md)
- [Feature Documentation Template](./Templates/FeatureDocumentationTemplate.md)

## 🚀 Quick Start

1. **New to the project?** Start with [Setup Guide](./Development/SetupGuide.md)
2. **Understanding the architecture?** Read [System Overview](./Architecture/SystemOverview.md)
3. **Working with networking?** Check [Networking Architecture](./Architecture/NetworkingArchitecture.md)
4. **Need API reference?** Browse [API Reference](./API/)
5. **Encountering issues?** Visit [Troubleshooting](./Troubleshooting/)

## 📝 Documentation Standards

- All documentation is written in **Markdown**
- System diagrams use **Mermaid** syntax
- Code examples include **language tags**
- Cross-references use **relative links**
- Updates follow **semantic versioning**

## 🔄 Keeping Documentation Updated

This documentation is a **living system** that should be updated alongside code changes:

- **New features** require corresponding documentation
- **API changes** must update relevant reference docs
- **Bug fixes** should update troubleshooting guides
- **Architecture changes** require system overview updates

## 📞 Support

For questions about this documentation or the project:

- Check [Common Issues](./Troubleshooting/CommonIssues.md) first
- Review relevant system documentation
- Consult API references for implementation details

## 🎯 Key Systems Quick Reference

### Core Architecture

- **MyNetworkManager** - Central networking with scene-aware spawning
- **SteamLobby** - Steam integration and lobby management
- **ForestPlayerManager** - Coordinated player spawning in Map_01
- **HelicopterImmobilizationManager** - Complete player immobilization during helicopter sequences
- **MainMenu** - UI state management and lobby interface

### Player Flow

1. **MainMenu** → Steam authentication and lobby creation
2. **Party System** → Ready state management and matchmaking
3. **Map_01 Transition** → Scene loading with player spawning
4. **Forest Intro** → Helicopter sequence with complete player immobilization
5. **Helicopter Landing** → Player control restoration and gameplay start

### Development Patterns

- **Singleton Managers** - `instance` property for global access
- **Scene-Specific Systems** - Dedicated managers per scene
- **Component Composition** - Dynamic component addition for scene behavior
- **Centralized Spawning** - Coordination between network and scene systems

---

**Last Updated:** December 2024
**Version:** 1.0.0
**Project:** TheLastStand Unity Multiplayer Game
