using UnityEngine;
using Mirror;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Validates scene transition prerequisites and system state to ensure smooth transitions
/// </summary>
public static class SceneTransitionValidator
{
    public enum ValidationResult
    {
        Success,
        Warning,
        Error
    }

    public struct ValidationReport
    {
        public ValidationResult result;
        public string message;
        public string category;
        public bool isBlockingIssue;

        public ValidationReport(ValidationResult result, string message, string category, bool isBlockingIssue = false)
        {
            this.result = result;
            this.message = message;
            this.category = category;
            this.isBlockingIssue = isBlockingIssue;
        }
    }

    /// <summary>
    /// Performs comprehensive validation before starting a scene transition
    /// </summary>
    public static List<ValidationReport> ValidateTransitionPrerequisites()
    {
        List<ValidationReport> reports = new List<ValidationReport>();

        // Network validation
        reports.AddRange(ValidateNetworkState());

        // Player validation
        reports.AddRange(ValidatePlayerState());

        // Steam integration validation
        reports.AddRange(ValidateSteamIntegration());

        // Scene validation
        reports.AddRange(ValidateSceneState());

        // System resources validation
        reports.AddRange(ValidateSystemResources());

        return reports;
    }

    /// <summary>
    /// Validates the current network state
    /// </summary>
    private static List<ValidationReport> ValidateNetworkState()
    {
        List<ValidationReport> reports = new List<ValidationReport>();

        // Check if NetworkManager exists
        if (NetworkManager.singleton == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "NetworkManager.singleton is null", "Network", true));
            return reports;
        }

        // Check server state
        if (!NetworkServer.active)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "NetworkServer is not active", "Network", true));
        }

        // Check MyNetworkManager instance
        if (MyNetworkManager.instance == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "MyNetworkManager.instance is null", "Network", true));
        }

        // Check player prefab
        if (NetworkManager.singleton.playerPrefab == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "Player prefab is not assigned in NetworkManager", "Network", true));
        }

        // Check connection limits
        if (NetworkServer.connections.Count > NetworkManager.singleton.maxConnections)
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                $"Connection count ({NetworkServer.connections.Count}) exceeds max connections ({NetworkManager.singleton.maxConnections})", 
                "Network"));
        }

        return reports;
    }

    /// <summary>
    /// Validates player state and readiness
    /// </summary>
    private static List<ValidationReport> ValidatePlayerState()
    {
        List<ValidationReport> reports = new List<ValidationReport>();

        // Check LobbyPlayerList
        if (LobbyPlayerList.instance == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "LobbyPlayerList.instance is null", "Players", true));
            return reports;
        }

        // Check if there are any players
        if (LobbyPlayerList.instance.allClients.Count == 0)
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                "No players in lobby", "Players"));
            return reports;
        }

        // Validate each player
        int readyPlayers = 0;
        int totalPlayers = LobbyPlayerList.instance.allClients.Count;

        foreach (MyClient client in LobbyPlayerList.instance.allClients)
        {
            if (client == null)
            {
                reports.Add(new ValidationReport(ValidationResult.Error, 
                    "Found null client in player list", "Players", true));
                continue;
            }

            if (client.connectionToClient == null)
            {
                reports.Add(new ValidationReport(ValidationResult.Error, 
                    $"Client {client.playerInfo.playerName} has null connection", "Players", true));
                continue;
            }

            if (client.IsReady)
            {
                readyPlayers++;
            }

            // Validate Steam ID if multiplayer
            if (MyNetworkManager.isMultiplayer && client.playerInfo.steamId == 0)
            {
                reports.Add(new ValidationReport(ValidationResult.Warning, 
                    $"Client {client.playerInfo.playerName} has invalid Steam ID", "Players"));
            }
        }

        // Check if all players are ready
        if (readyPlayers < totalPlayers)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                $"Only {readyPlayers}/{totalPlayers} players are ready", "Players", true));
        }
        else
        {
            reports.Add(new ValidationReport(ValidationResult.Success, 
                $"All {totalPlayers} players are ready", "Players"));
        }

        return reports;
    }

    /// <summary>
    /// Validates Steam integration if in multiplayer mode
    /// </summary>
    private static List<ValidationReport> ValidateSteamIntegration()
    {
        List<ValidationReport> reports = new List<ValidationReport>();

        if (!MyNetworkManager.isMultiplayer)
        {
            reports.Add(new ValidationReport(ValidationResult.Success, 
                "Single-player mode, Steam validation skipped", "Steam"));
            return reports;
        }

        // Check if Steam is initialized
        if (!SteamManager.Initialized)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "Steam is not initialized", "Steam", true));
            return reports;
        }

        // Check SteamLobby
        if (SteamLobby.LobbyID.IsValid())
        {
            reports.Add(new ValidationReport(ValidationResult.Success, 
                "Steam lobby is valid", "Steam"));
        }
        else
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                "Steam lobby ID is not valid", "Steam"));
        }

        // Check Steam transport
        var transport = NetworkManager.singleton.transport;
        if (transport == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "Network transport is null", "Steam", true));
        }
        else if (transport.GetType().Name.Contains("Steam"))
        {
            reports.Add(new ValidationReport(ValidationResult.Success, 
                "Steam transport is configured", "Steam"));
        }
        else
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                "Non-Steam transport detected in multiplayer mode", "Steam"));
        }

        return reports;
    }

    /// <summary>
    /// Validates current scene state
    /// </summary>
    private static List<ValidationReport> ValidateSceneState()
    {
        List<ValidationReport> reports = new List<ValidationReport>();

        // Check current scene
        string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        if (currentScene != "MainMenu")
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                $"Expected MainMenu scene but current scene is '{currentScene}'", "Scene"));
        }

        // Check if target scene can be loaded
        if (!Application.CanStreamedLevelBeLoaded("Map_01"))
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "Target scene 'Map_01' cannot be loaded", "Scene", true));
        }

        // Check essential MainMenu components
        if (MainMenu.instance == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                "MainMenu.instance is null", "Scene"));
        }

        if (LobbyController.instance == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "LobbyController.instance is null", "Scene", true));
        }

        return reports;
    }

    /// <summary>
    /// Validates system resources and performance
    /// </summary>
    private static List<ValidationReport> ValidateSystemResources()
    {
        List<ValidationReport> reports = new List<ValidationReport>();

        // Check memory usage
        long memoryUsage = System.GC.GetTotalMemory(false);
        float memoryMB = memoryUsage / (1024f * 1024f);
        
        if (memoryMB > 1000f) // 1GB threshold
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                $"High memory usage: {memoryMB:F1} MB", "Performance"));
        }

        // Check frame rate
        float frameRate = 1f / Time.unscaledDeltaTime;
        if (frameRate < 30f)
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                $"Low frame rate: {frameRate:F1} FPS", "Performance"));
        }

        // Check if game is paused
        if (Time.timeScale == 0f)
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                "Game is paused (timeScale = 0)", "Performance"));
        }

        return reports;
    }

    /// <summary>
    /// Validates Map_01 scene prerequisites after loading
    /// </summary>
    public static List<ValidationReport> ValidateMap01SceneState()
    {
        List<ValidationReport> reports = new List<ValidationReport>();

        // Check scene name
        string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        if (currentScene != "Map_01")
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                $"Expected Map_01 scene but current scene is '{currentScene}'", "Scene", true));
            return reports;
        }

        // Check ForestGameManager
        ForestGameManager forestGameManager = Object.FindObjectOfType<ForestGameManager>();
        if (forestGameManager == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "ForestGameManager not found in Map_01 scene", "Scene", true));
        }

        // Check ForestPlayerManager
        ForestPlayerManager forestPlayerManager = Object.FindObjectOfType<ForestPlayerManager>();
        if (forestPlayerManager == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "ForestPlayerManager not found in Map_01 scene", "Scene", true));
        }
        else if (!forestPlayerManager.ValidateManagerState())
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "ForestPlayerManager validation failed", "Scene", true));
        }

        // Check ForestIntroHelicopter
        ForestIntroHelicopter helicopter = Object.FindObjectOfType<ForestIntroHelicopter>();
        if (helicopter == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Error, 
                "ForestIntroHelicopter not found in Map_01 scene", "Scene", true));
        }

        // Check HelicopterImmobilizationManager
        HelicopterImmobilizationManager immobilizationManager = Object.FindObjectOfType<HelicopterImmobilizationManager>();
        if (immobilizationManager == null)
        {
            reports.Add(new ValidationReport(ValidationResult.Warning, 
                "HelicopterImmobilizationManager not found in Map_01 scene", "Scene"));
        }

        return reports;
    }

    /// <summary>
    /// Checks if validation reports contain any blocking issues
    /// </summary>
    public static bool HasBlockingIssues(List<ValidationReport> reports)
    {
        return reports.Any(r => r.isBlockingIssue);
    }

    /// <summary>
    /// Gets a summary of validation results
    /// </summary>
    public static string GetValidationSummary(List<ValidationReport> reports)
    {
        int errors = reports.Count(r => r.result == ValidationResult.Error);
        int warnings = reports.Count(r => r.result == ValidationResult.Warning);
        int successes = reports.Count(r => r.result == ValidationResult.Success);
        int blocking = reports.Count(r => r.isBlockingIssue);

        return $"Validation Summary: {successes} Success, {warnings} Warnings, {errors} Errors ({blocking} Blocking)";
    }

    /// <summary>
    /// Logs all validation reports to console
    /// </summary>
    public static void LogValidationReports(List<ValidationReport> reports, string context = "")
    {
        string prefix = string.IsNullOrEmpty(context) ? "SceneTransitionValidator" : $"SceneTransitionValidator ({context})";
        
        Debug.Log($"{prefix}: {GetValidationSummary(reports)}");

        foreach (var report in reports)
        {
            string logMessage = $"{prefix} [{report.category}]: {report.message}";
            
            switch (report.result)
            {
                case ValidationResult.Success:
                    Debug.Log(logMessage);
                    break;
                case ValidationResult.Warning:
                    Debug.LogWarning(logMessage);
                    break;
                case ValidationResult.Error:
                    Debug.LogError(logMessage);
                    break;
            }
        }
    }
}
