using UnityEngine;
using Mirror;

public class ForestGameManager : GameManager
{

    [SerializeField] private ForestIntroHelicopter forestIntroHelicopter;
    [SerializeField] private ForestMap_AudioManager forestMapAudioManager;
    [SerializeField] private ForestPlayerManager forestPlayerManager;
    [SerializeField] private HelicopterImmobilizationManager immobilizationManager;

    public void Start()
    {
        // Ensure we have a reference to the ForestPlayerManager
        if (forestPlayerManager == null)
        {
            forestPlayerManager = FindObjectOfType<ForestPlayerManager>();
        }

        // Ensure we have a reference to the HelicopterImmobilizationManager
        if (immobilizationManager == null)
        {
            immobilizationManager = FindObjectOfType<HelicopterImmobilizationManager>();
            if (immobilizationManager == null)
            {
                Debug.LogWarning("ForestGameManager: HelicopterImmobilizationManager not found. Player immobilization during helicopter sequence may not work.");
            }
        }

        // Start intro sequence with slight delay to ensure all systems are ready
        if (forestIntroHelicopter != null)
        {
            forestIntroHelicopter.Invoke("StartIntroSequence", 1f);
        }
        else
        {
            Debug.LogError("ForestGameManager: ForestIntroHelicopter not assigned in the Inspector.");
        }

        if (forestMapAudioManager != null)
        {
            forestMapAudioManager.PlayIntroStormSound();
            forestMapAudioManager.PlayIntroBackgroundMusic();
            forestMapAudioManager.Invoke("PlayNarratorIntro", 2f);
        }
        else
        {
            Debug.LogError("ForestGameManager: ForestMapAudioManager not assigned in the Inspector.");
        }

        // Log the current networking state for debugging
        Debug.Log($"ForestGameManager: Scene started. Multiplayer: {MyNetworkManager.isMultiplayer}, NetworkServer.active: {NetworkServer.active}");
    }

    public void OnHelicopterLanded()
    {
      // Fade out the helicopter sound
      AudioSettings.Instance.FadeOutSound(forestIntroHelicopter.GetComponent<AudioSource>(), 2f);

      
    }
    
}
