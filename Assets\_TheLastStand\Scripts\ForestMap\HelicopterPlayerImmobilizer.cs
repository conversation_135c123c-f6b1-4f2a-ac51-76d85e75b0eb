using UnityEngine;
using Mirror;
using System.Collections.Generic;

/// <summary>
/// Completely immobilizes a player during helicopter intro sequences.
/// Disables all movement, input, physics, and enforces position locking.
/// Works in conjunction with HelicopterImmobilizationManager for coordinated control.
/// </summary>
public class HelicopterPlayerImmobilizer : NetworkBehaviour
{
    [Header("Immobilization Settings")]
    [SerializeField] private bool immobilizeOnStart = false;
    [SerializeField] private Vector3 lockedLocalPosition = Vector3.zero;
    [SerializeField] private bool lockRotation = true;
    [SerializeField] private bool disablePhysics = true;
    [SerializeField] private bool disableMovementInput = true;
    [SerializeField] private bool disableCameraInput = false; // Allow camera look during helicopter ride

    [Header("Debug Settings")]
    [SerializeField] private bool showDebugLogs = false;

    // Network synchronized immobilization state
    [SyncVar(hook = nameof(OnImmobilizationActiveChanged))]
    private bool immobilizationActive = false;

    // Component references
    private Transform spawnPointParent;
    private Vector3 initialLocalPosition;
    private Quaternion initialLocalRotation;

    // Movement system components
    private Movement movementComponent;
    private ForestPlayer forestPlayerComponent;
    private ForestIntroPlayer forestIntroPlayerComponent;
    private CharacterController characterController;
    private UnityEngine.AI.NavMeshAgent navMeshAgent;
    private Rigidbody rigidBody;

    // Component state tracking for restoration
    private struct ComponentState
    {
        public bool movementEnabled;
        public bool forestPlayerEnabled;
        public bool forestIntroPlayerEnabled;
        public bool characterControllerEnabled;
        public bool navMeshAgentEnabled;
        public bool rigidBodyKinematic;
        public bool rigidBodyUseGravity;
    }

    private ComponentState savedComponentState;
    private bool hasValidSpawnPoint = false;

    public bool ImmobilizationActive => immobilizationActive;
    public bool HasValidSpawnPoint => hasValidSpawnPoint;

    public override void OnStartAuthority()
    {
        base.OnStartAuthority();
        InitializeImmobilizer();
    }

    public override void OnStartClient()
    {
        base.OnStartClient();
        if (!isOwned)
        {
            InitializeImmobilizer();
        }
    }

    private void Start()
    {
        InitializeImmobilizer();
    }

    private void InitializeImmobilizer()
    {
        // Get component references
        CacheComponentReferences();

        // Store spawn point parent (should be set during spawning)
        spawnPointParent = transform.parent;
        hasValidSpawnPoint = spawnPointParent != null;

        if (hasValidSpawnPoint)
        {
            initialLocalPosition = transform.localPosition;
            initialLocalRotation = transform.localRotation;

            if (showDebugLogs)
            {
                Debug.Log($"HelicopterPlayerImmobilizer: Initialized for {gameObject.name} with spawn point {spawnPointParent.name}");
            }
        }
        else
        {
            Debug.LogWarning($"HelicopterPlayerImmobilizer: No spawn point parent found for {gameObject.name}. Immobilization may not work correctly.");
        }

        // Register with immobilization manager
        HelicopterImmobilizationManager.Instance?.RegisterPlayerImmobilizer(this);

        // Apply immobilization if requested on start
        if (immobilizeOnStart && isServer)
        {
            ActivateImmobilization();
        }
    }

    private void CacheComponentReferences()
    {
        movementComponent = GetComponent<Movement>();
        forestPlayerComponent = GetComponent<ForestPlayer>();
        forestIntroPlayerComponent = GetComponent<ForestIntroPlayer>();
        characterController = GetComponent<CharacterController>();
        navMeshAgent = GetComponent<UnityEngine.AI.NavMeshAgent>();
        rigidBody = GetComponent<Rigidbody>();

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterPlayerImmobilizer: Cached components for {gameObject.name} - " +
                     $"Movement: {movementComponent != null}, ForestPlayer: {forestPlayerComponent != null}, " +
                     $"ForestIntroPlayer: {forestIntroPlayerComponent != null}, CharacterController: {characterController != null}, " +
                     $"NavMeshAgent: {navMeshAgent != null}, Rigidbody: {rigidBody != null}");
        }
    }

    private void Update()
    {
        // Continuously enforce position locking when immobilization is active
        if (immobilizationActive && hasValidSpawnPoint)
        {
            EnforcePositionLocking();
        }
    }

    private void EnforcePositionLocking()
    {
        if (spawnPointParent == null) return;

        // Force local position to locked position
        if (transform.localPosition != lockedLocalPosition)
        {
            transform.localPosition = lockedLocalPosition;
        }

        // Force rotation if locked
        if (lockRotation && transform.localRotation != initialLocalRotation)
        {
            transform.localRotation = initialLocalRotation;
        }
    }

    /// <summary>
    /// Activates complete player immobilization
    /// </summary>
    [Server]
    public void ActivateImmobilization()
    {
        if (!isServer) return;

        if (immobilizationActive) return; // Already active

        // Save current component states
        SaveComponentStates();

        // Disable all movement and physics systems
        DisableMovementSystems();
        
        if (disablePhysics)
        {
            DisablePhysicsSystems();
        }

        // Set immobilization active
        immobilizationActive = true;

        // Apply immediate position correction
        if (hasValidSpawnPoint)
        {
            ApplyImmediatePositionCorrection();
        }

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterPlayerImmobilizer: Activated immobilization for {gameObject.name}");
        }
    }

    /// <summary>
    /// Deactivates player immobilization and restores all systems
    /// </summary>
    [Server]
    public void DeactivateImmobilization()
    {
        if (!isServer) return;

        if (!immobilizationActive) return; // Already inactive

        // Restore all component states
        RestoreComponentStates();

        // Set immobilization inactive
        immobilizationActive = false;

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterPlayerImmobilizer: Deactivated immobilization for {gameObject.name}");
        }
    }

    private void SaveComponentStates()
    {
        savedComponentState = new ComponentState
        {
            movementEnabled = movementComponent != null ? movementComponent.enabled : false,
            forestPlayerEnabled = forestPlayerComponent != null ? forestPlayerComponent.enabled : false,
            forestIntroPlayerEnabled = forestIntroPlayerComponent != null ? forestIntroPlayerComponent.enabled : false,
            characterControllerEnabled = characterController != null ? characterController.enabled : false,
            navMeshAgentEnabled = navMeshAgent != null ? navMeshAgent.enabled : false,
            rigidBodyKinematic = rigidBody != null ? rigidBody.isKinematic : false,
            rigidBodyUseGravity = rigidBody != null ? rigidBody.useGravity : false
        };

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterPlayerImmobilizer: Saved component states for {gameObject.name}");
        }
    }

    private void DisableMovementSystems()
    {
        // Disable movement components
        if (movementComponent != null)
        {
            movementComponent.enabled = false;
        }

        // Disable ForestPlayer movement (but keep component active for other functionality)
        if (forestPlayerComponent != null && disableMovementInput)
        {
            // ForestPlayer will check immobilization state in its movement methods
            // We don't disable the entire component to preserve other functionality
        }

        // Disable ForestIntroPlayer movement input
        if (forestIntroPlayerComponent != null && disableMovementInput)
        {
            // ForestIntroPlayer will check immobilization state for input handling
        }

        // Disable CharacterController
        if (characterController != null)
        {
            characterController.enabled = false;
        }

        // Disable NavMeshAgent
        if (navMeshAgent != null)
        {
            navMeshAgent.enabled = false;
        }

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterPlayerImmobilizer: Disabled movement systems for {gameObject.name}");
        }
    }

    private void DisablePhysicsSystems()
    {
        // Disable Rigidbody physics
        if (rigidBody != null)
        {
            rigidBody.isKinematic = true;
            rigidBody.useGravity = false;
        }

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterPlayerImmobilizer: Disabled physics systems for {gameObject.name}");
        }
    }

    private void RestoreComponentStates()
    {
        // Restore movement components
        if (movementComponent != null)
        {
            movementComponent.enabled = savedComponentState.movementEnabled;
        }

        if (forestPlayerComponent != null)
        {
            forestPlayerComponent.enabled = savedComponentState.forestPlayerEnabled;
        }

        if (forestIntroPlayerComponent != null)
        {
            forestIntroPlayerComponent.enabled = savedComponentState.forestIntroPlayerEnabled;
        }

        if (characterController != null)
        {
            characterController.enabled = savedComponentState.characterControllerEnabled;
        }

        if (navMeshAgent != null)
        {
            navMeshAgent.enabled = savedComponentState.navMeshAgentEnabled;
        }

        // Restore Rigidbody physics
        if (rigidBody != null)
        {
            rigidBody.isKinematic = savedComponentState.rigidBodyKinematic;
            rigidBody.useGravity = savedComponentState.rigidBodyUseGravity;
        }

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterPlayerImmobilizer: Restored component states for {gameObject.name}");
        }
    }

    private void ApplyImmediatePositionCorrection()
    {
        if (spawnPointParent == null) return;

        // Force position to locked position
        transform.localPosition = lockedLocalPosition;

        if (lockRotation)
        {
            transform.localRotation = initialLocalRotation;
        }

        if (showDebugLogs)
        {
            Debug.Log($"HelicopterPlayerImmobilizer: Applied immediate position correction for {gameObject.name}");
        }
    }

    private void OnImmobilizationActiveChanged(bool oldValue, bool newValue)
    {
        // This hook is called on all clients when the SyncVar changes
        if (showDebugLogs)
        {
            Debug.Log($"HelicopterPlayerImmobilizer: Immobilization state changed to {newValue} for {gameObject.name}");
        }

        // Apply immediate position correction when immobilization becomes active
        if (newValue && hasValidSpawnPoint)
        {
            ApplyImmediatePositionCorrection();
        }
    }

    /// <summary>
    /// Checks if movement input should be blocked
    /// Called by movement components to determine if they should process input
    /// </summary>
    public bool ShouldBlockMovementInput()
    {
        return immobilizationActive && disableMovementInput;
    }

    /// <summary>
    /// Checks if camera input should be blocked
    /// Called by camera components to determine if they should process input
    /// </summary>
    public bool ShouldBlockCameraInput()
    {
        return immobilizationActive && disableCameraInput;
    }

    private void OnDestroy()
    {
        // Unregister from immobilization manager
        HelicopterImmobilizationManager.Instance?.UnregisterPlayerImmobilizer(this);
    }

    private void OnDrawGizmosSelected()
    {
        if (hasValidSpawnPoint && spawnPointParent != null)
        {
            // Draw locked position
            Vector3 worldLockedPosition = spawnPointParent.TransformPoint(lockedLocalPosition);
            Gizmos.color = immobilizationActive ? Color.red : Color.yellow;
            Gizmos.DrawWireSphere(worldLockedPosition, 0.5f);
            
            // Draw line from current position to locked position
            Gizmos.color = Color.cyan;
            Gizmos.DrawLine(transform.position, worldLockedPosition);
        }
    }
}
