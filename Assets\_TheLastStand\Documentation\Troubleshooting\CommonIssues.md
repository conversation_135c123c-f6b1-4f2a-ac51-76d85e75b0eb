# Common Issues - TheLastStand

## 🔧 Overview

This guide covers the most frequently encountered issues in TheLastStand development and their solutions.

## 🌐 Networking Issues

### Duplicate Player Spawning
**Problem:** Multiple player instances are created for the same client  
**Symptoms:**
- Two or more player objects for one client
- Camera conflicts
- Input not working properly

**Solution:**
1. Check ForestPlayerManager coordination with MyNetworkManager
2. Verify scene-specific spawning logic in `OnServerAddPlayer`
3. Ensure proper cleanup in scene transitions

**Code Check:**
```csharp
// In MyNetworkManager.OnServerAddPlayer()
string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;

if (currentScene == "Map_01")
{
    HandleForestMapSpawning(conn); // Should delegate to ForestPlayerManager
}
```

### Players Not Spawning
**Problem:** Players connect but no player object is created  
**Symptoms:**
- Connection established but no visible player
- Missing player in LobbyPlayerList
- Network identity errors

**Solution:**
1. Check playerPrefab is assigned in MyNetworkManager
2. Verify spawn points are configured in ForestPlayerManager
3. Check for exceptions in spawning code

**Debug Steps:**
```csharp
// Add debug logging in MyNetworkManager
Debug.Log($"Spawning player for connection {conn.connectionId} in scene {currentScene}");
```

### Connection Timeouts
**Problem:** Clients can't connect to host or connection drops  
**Symptoms:**
- "Connection failed" messages
- Clients stuck on "Connecting..."
- Host can't be found

**Solution:**
1. Check firewall settings (allow Unity Editor and builds)
2. Verify Steam lobby is created successfully
3. Test on local network first
4. Check NetworkManager transport settings

## 🎮 Steam Integration Issues

### Steam Not Initializing
**Problem:** Steam API fails to initialize  
**Symptoms:**
- "Steam not initialized" errors
- Steam features not working
- Lobby creation fails

**Solution:**
1. Ensure Steam client is running
2. Check `steam_appid.txt` exists in project root
3. Verify valid App ID (use 480 for testing)
4. Check SteamManager is in MainMenu scene

**File Check:**
```
ProjectRoot/
├── steam_appid.txt (contains: 480)
└── Assets/
```

### Lobby Creation Fails
**Problem:** Steam lobby creation returns error  
**Symptoms:**
- "Failed to create lobby" popup
- OnLobbyCreated callback with error
- Can't start hosting

**Solution:**
1. Check Steam is logged in
2. Verify network connection
3. Check Steam overlay is enabled
4. Try different lobby settings

**Debug Code:**
```csharp
// In SteamLobby.OnLobbyCreated()
if (callback.m_eResult != EResult.k_EResultOK)
{
    Debug.LogError($"Lobby creation failed: {callback.m_eResult}");
}
```

### Friends Can't Join
**Problem:** Friends can't see or join lobby  
**Symptoms:**
- Lobby not visible to friends
- Join attempts fail
- "Lobby full" errors when not full

**Solution:**
1. Check lobby is set to public
2. Verify lobby data is set correctly
3. Check maxConnections setting
4. Test with Steam overlay invite

## 🎬 Scene Issues

### Scene Transition Hangs
**Problem:** Game gets stuck during scene transitions  
**Symptoms:**
- Loading screen never ends
- Players stuck between scenes
- Network disconnections during transition

**Solution:**
1. Check all scenes are in build settings
2. Verify scene names match exactly
3. Check for exceptions during scene load
4. Ensure proper cleanup before transition

**Build Settings Check:**
```
Build Settings:
0. MainMenu
1. Map_01
```

### Forest Intro Not Starting
**Problem:** Helicopter intro sequence doesn't play
**Symptoms:**
- Players spawn but no helicopter
- No intro audio
- Missing intro UI

**Solution:**
1. Check ForestGameManager is in Map_01 scene
2. Verify ForestIntroHelicopter is assigned
3. Check helicopter waypoints are configured
4. Verify audio manager references
5. Ensure HelicopterImmobilizationManager is in scene

**Component Check:**
```csharp
// In ForestGameManager.Start()
if (forestIntroHelicopter != null)
{
    forestIntroHelicopter.Invoke("StartIntroSequence", 1f);
}
else
{
    Debug.LogError("ForestIntroHelicopter not assigned!");
}
```

## 🎵 Audio Issues

### No Audio Playing
**Problem:** Audio systems not working  
**Symptoms:**
- Complete silence
- Missing background music
- No sound effects

**Solution:**
1. Check AudioSettings instance exists
2. Verify audio sources are assigned
3. Check volume settings
4. Verify audio clips are assigned

### Audio Overlapping
**Problem:** Multiple audio tracks playing simultaneously  
**Symptoms:**
- Layered music tracks
- Echo effects
- Audio chaos

**Solution:**
1. Check audio manager cleanup
2. Verify proper audio transitions
3. Stop previous audio before starting new
4. Check for multiple audio managers

## 🎮 Player Control Issues

### Camera Not Working
**Problem:** Player camera doesn't respond to input  
**Symptoms:**
- Fixed camera view
- No mouse look
- Camera stuck in one position

**Solution:**
1. Check ForestIntroPlayer component is added
2. Verify camera is assigned to local player
3. Check cursor lock state
4. Verify input system is working

**Debug Check:**
```csharp
// In ForestIntroPlayer
if (cameraTransform == null)
{
    Debug.LogError("Camera not assigned to ForestIntroPlayer!");
}
```

### Input Not Responding
**Problem:** Player input doesn't work
**Symptoms:**
- No movement response
- Mouse look not working
- UI not responding

**Solution:**
1. Check Input System package is installed
2. Verify input actions are configured
3. Check for multiple input components
4. Verify player has authority
5. Check if player is immobilized during helicopter sequence

### Players Not Immobilized During Helicopter
**Problem:** Players can move during helicopter intro sequence
**Symptoms:**
- Players fall through helicopter
- Movement works during helicopter ride
- Players not locked to spawn points

**Solution:**
1. Check HelicopterImmobilizationManager is in scene
2. Verify players have HelicopterPlayerImmobilizer components
3. Check helicopter state machine is working
4. Verify immobilization activation in debug logs

**Debug Check:**
```csharp
// Check immobilization status
var immobilizer = player.GetComponent<HelicopterPlayerImmobilizer>();
Debug.Log($"Immobilized: {immobilizer.ImmobilizationActive}");
Debug.Log($"Should block movement: {immobilizer.ShouldBlockMovementInput()}");
```

## 🔧 Build Issues

### Build Fails
**Problem:** Unity build process fails  
**Symptoms:**
- Compilation errors
- Missing references
- Build process stops

**Solution:**
1. Fix all compilation errors first
2. Check all required packages are installed
3. Verify scenes are added to build settings
4. Clear Library folder and reimport

### Missing DLLs in Build
**Problem:** Steamworks DLLs missing from build  
**Symptoms:**
- Steam features don't work in build
- DLL not found errors
- Steam initialization fails

**Solution:**
1. Check Steamworks.NET DLLs are in Plugins folder
2. Verify platform settings for DLLs
3. Check build target matches DLL architecture
4. Manually copy DLLs if needed

## 🔍 Debugging Tools

### Unity Console
Check Unity Console for error messages:
- **Red errors** - Critical issues that must be fixed
- **Yellow warnings** - Potential problems
- **White logs** - Information messages

### Network Profiler
Use Unity's Network Profiler to debug networking:
1. Window > Analysis > Profiler
2. Select Network module
3. Monitor network traffic and messages

### Steam Logs
Check Steam logs for integration issues:
- Steam client console (steam://open/console)
- Unity console for Steam-related errors
- Steamworks.NET debug output

## 📞 Getting Help

### Before Asking for Help
1. Check this troubleshooting guide
2. Search Unity Console for error messages
3. Verify your setup matches the Setup Guide
4. Try reproducing the issue in a clean project

### Information to Provide
When reporting issues, include:
- Unity version
- Exact error messages
- Steps to reproduce
- System specifications
- Build vs Editor behavior differences

### Useful Debug Commands
```csharp
// Network debugging
Debug.Log($"NetworkServer.active: {NetworkServer.active}");
Debug.Log($"NetworkClient.active: {NetworkClient.active}");
Debug.Log($"Connected players: {NetworkServer.connections.Count}");

// Steam debugging
Debug.Log($"Steam initialized: {SteamManager.Initialized}");
Debug.Log($"Steam ID: {SteamUser.GetSteamID()}");
Debug.Log($"Lobby ID: {SteamLobby.LobbyID}");
```

---

**Next:** [Networking Issues](./NetworkingIssues.md) | [Steam Issues](./SteamIssues.md)
