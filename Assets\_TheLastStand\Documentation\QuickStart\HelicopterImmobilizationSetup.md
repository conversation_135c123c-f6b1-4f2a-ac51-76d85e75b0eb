# Helicopter Immobilization System - Quick Setup Guide

## Automatic Setup (Recommended)

The helicopter immobilization system is designed to work automatically with minimal setup required.

### Prerequisites
1. Ensure your scene has a `ForestIntroHelicopter` component
2. Ensure your scene has a `HelicopterExitManager` component
3. Ensure your scene has a `ForestGameManager` component
4. Players should be spawned using the `ForestPlayerManager` system

### Automatic Integration
The system automatically:
- Adds `HelicopterPlayerImmobilizer` components to all players during spawning
- Activates immobilization when helicopter begins flight
- Deactivates immobilization when helicopter lands or exit sequence begins
- Synchronizes immobilization states across all networked clients

## Manual Setup (If Needed)

### 1. Add HelicopterImmobilizationManager to Scene

Create an empty GameObject in your Forest Map scene and add the `HelicopterImmobilizationManager` component:

```csharp
// The component will auto-find helicopter and exit manager references
// Or assign them manually in the inspector:
[SerializeField] private ForestIntroHelicopter helicopter;
[SerializeField] private HelicopterExitManager exitManager;
```

### 2. Configure ForestGameManager

Ensure your `ForestGameManager` has a reference to the immobilization manager:

```csharp
[SerializeField] private HelicopterImmobilizationManager immobilizationManager;
```

### 3. Add Immobilizer Components to Players

If not using `ForestPlayerManager`, manually add `HelicopterPlayerImmobilizer` to player prefabs:

```csharp
// Add component to player GameObject
HelicopterPlayerImmobilizer immobilizer = player.AddComponent<HelicopterPlayerImmobilizer>();
```

## Configuration Options

### Global Settings (HelicopterImmobilizationManager)

**Immobilization Configuration:**
- `Enable Immobilization During Flight`: Auto-activate during helicopter movement (default: true)
- `Disable Immobilization On Landing`: Auto-deactivate when helicopter lands (default: true)
- `Disable Immobilization On Exit`: Auto-deactivate when exit sequence begins (default: true)
- `Activation Delay`: Delay before activating immobilization (default: 0.5s)
- `Deactivation Delay`: Delay before deactivating immobilization (default: 1.0s)

**Debug Settings:**
- `Show Debug Logs`: Enable detailed logging for troubleshooting (default: true)

### Per-Player Settings (HelicopterPlayerImmobilizer)

**Immobilization Settings:**
- `Locked Local Position`: Position relative to spawn point (default: 0,0,0)
- `Lock Rotation`: Whether to lock player rotation (default: true)
- `Disable Physics`: Whether to disable gravity/physics (default: true)
- `Disable Movement Input`: Whether to block movement input (default: true)
- `Disable Camera Input`: Whether to block camera input (default: false)

**Debug Settings:**
- `Show Debug Logs`: Enable per-player debug logging (default: false)

## Testing the System

### 1. Single Player Testing
1. Start the Forest Map scene in single-player mode
2. Observe that players are immobilized during helicopter flight
3. Verify players can move again after helicopter lands

### 2. Multiplayer Testing
1. Host a multiplayer session and load Forest Map
2. Have multiple clients join
3. Verify all players are immobilized simultaneously
4. Check that immobilization state is synchronized across clients

### 3. Debug Verification
Enable debug logs and check console for:
```
HelicopterImmobilizationManager: Helicopter state changed to MovingToWaypoint
HelicopterImmobilizationManager: Activating immobilization for all players
HelicopterPlayerImmobilizer: Activated immobilization for NetworkedHost_ForestPlayer
```

## Troubleshooting

### Players Not Immobilized
**Check:**
1. HelicopterImmobilizationManager is in scene and active
2. Players have HelicopterPlayerImmobilizer components
3. Helicopter state machine is working correctly
4. Debug logs show immobilization activation

**Solution:**
```csharp
// Force activate immobilization for testing
HelicopterImmobilizationManager.Instance.ForceActivateImmobilization();
```

### Players Stuck After Landing
**Check:**
1. Helicopter state transitions to Landing/PathComplete
2. Exit sequence is properly configured
3. Component states are being restored

**Solution:**
```csharp
// Force deactivate immobilization
HelicopterImmobilizationManager.Instance.ForceDeactivateImmobilization();
```

### Movement Still Works During Immobilization
**Check:**
1. Movement components are checking immobilization state
2. ShouldBlockMovementInput() returns true
3. Component integration is correct

**Debug:**
```csharp
var immobilizer = player.GetComponent<HelicopterPlayerImmobilizer>();
Debug.Log($"Should block movement: {immobilizer.ShouldBlockMovementInput()}");
Debug.Log($"Immobilization active: {immobilizer.ImmobilizationActive}");
```

## Advanced Configuration

### Custom Timing
```csharp
// Adjust timing in HelicopterImmobilizationManager
[SerializeField] private float activationDelay = 0.5f;
[SerializeField] private float deactivationDelay = 1.0f;
```

### Custom Position Locking
```csharp
// Adjust locked position in HelicopterPlayerImmobilizer
[SerializeField] private Vector3 lockedLocalPosition = Vector3.zero;
```

### Selective Input Blocking
```csharp
// Allow camera movement during helicopter ride
[SerializeField] private bool disableCameraInput = false;
```

## Integration with Existing Systems

The immobilization system integrates seamlessly with:
- **HelicopterConstraintManager**: Works alongside existing constraint system
- **ForestPlayerManager**: Automatic component addition during spawning
- **Movement Systems**: All movement components check immobilization state
- **Networking**: Full Mirror networking support with state synchronization

## Performance Considerations

- Minimal performance impact during normal gameplay
- Efficient state checking in movement components
- Network traffic only for state changes, not continuous updates
- Automatic cleanup when players are destroyed
