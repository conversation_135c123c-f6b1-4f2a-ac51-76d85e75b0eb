using UnityEngine;
using System.Collections.Generic;
using Mirror;

public class ForestPlayerManager : MonoBehaviour
{
    [Header("Prefabs")]
    [SerializeField] private GameObject clientPlayerPrefab;
    [SerializeField] private GameObject replicatedPlayerPrefabOne;
    [SerializeField] private GameObject replicatedPlayerPrefabTwo;
    [SerializeField] private GameObject replicatedPlayerPrefabThree;
    [Header("Scene References")]
    [SerializeField] private Camera playerCameraToAssign;

    [Header("Spawn Points")]
    [SerializeField] private Transform localPlayerSpawnPoint;
    [SerializeField] private List<Transform> replicatedPlayerSpawnPoints = new List<Transform>();

    private ForestIntroPlayer _localPlayerInstance;
    private List<ForestIntroReplicatedPlayer> _replicatedPlayerInstances = new List<ForestIntroReplicatedPlayer>();
    private List<GameObject> _networkedPlayers = new List<GameObject>();
    private bool _hasSpawnedNetworkedPlayers = false;

    void Start()
    {
        // Only spawn non-networked intro players if we're not in multiplayer mode
        if (!MyNetworkManager.isMultiplayer)
        {
            SpawnLocalPlayer();
            SpawnReplicatedPlayers();
        }
        else
        {
            // In multiplayer, wait for network spawning to complete
        }
    }

    void SpawnLocalPlayer()
    {
        if (clientPlayerPrefab == null)
        {
            Debug.LogError("ForestPlayerManager: 'clientPlayerPrefab' is not assigned.", this);
            return;
        }
        if (localPlayerSpawnPoint == null)
        {
            Debug.LogError("ForestPlayerManager: 'localPlayerSpawnPoint' is not assigned. Cannot spawn local player.", this);
            return;
        }

        // Spawn player as child of spawn point with unified approach
        GameObject localPlayerGO = SpawnPlayerAtPoint(clientPlayerPrefab, localPlayerSpawnPoint, "Local_ForestIntroPlayer");

        _localPlayerInstance = localPlayerGO.GetComponent<ForestIntroPlayer>();
        if (_localPlayerInstance == null)
        {
            Debug.LogError("ForestPlayerManager: The 'clientPlayerPrefab' does not have a ForestIntroPlayer component.", localPlayerGO);
            Destroy(localPlayerGO);
            return;
        }

        // Configure camera
        if (playerCameraToAssign != null)
        {
            _localPlayerInstance.InitializePlayerCamera(playerCameraToAssign);
        }
        else
        {
            _localPlayerInstance.InitializePlayerCamera(null);
        }

        // Add constraint component
        AddConstraintComponent(localPlayerGO);
    }





    void SpawnReplicatedPlayers()
    {
        List<GameObject> availableReplicatedPrefabs = new List<GameObject>();
        if (replicatedPlayerPrefabOne != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabOne);
        if (replicatedPlayerPrefabTwo != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabTwo);
        if (replicatedPlayerPrefabThree != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabThree);

        if (availableReplicatedPrefabs.Count == 0)
        {
            Debug.LogError("ForestPlayerManager: No replicated player prefabs assigned (One, Two, or Three). Cannot spawn replicated players.", this);
            return;
        }

        if (replicatedPlayerSpawnPoints.Count == 0)
        {
            return;
        }

        int numberOfPlayersToSpawn = Mathf.Min(replicatedPlayerSpawnPoints.Count, availableReplicatedPrefabs.Count);

        for (int i = 0; i < numberOfPlayersToSpawn; i++)
        {
            Transform spawnPoint = replicatedPlayerSpawnPoints[i];
            GameObject prefabToSpawn = availableReplicatedPrefabs[i];

            if (spawnPoint == null || prefabToSpawn == null)
            {
                continue;
            }

            // Spawn replicated player using unified approach
            GameObject replicatedPlayerGO = SpawnPlayerAtPoint(prefabToSpawn, spawnPoint, $"Replicated_ForestIntroPlayer_{i}_{prefabToSpawn.name}");

            ForestIntroReplicatedPlayer replicatedPlayer = replicatedPlayerGO.GetComponent<ForestIntroReplicatedPlayer>();
            if (replicatedPlayer == null)
            {
                Debug.LogError($"ForestPlayerManager: The prefab \"{prefabToSpawn.name}\" does not have a ForestIntroReplicatedPlayer component. Spawned for index {i}.", replicatedPlayerGO);
                Destroy(replicatedPlayerGO);
                continue;
            }

            _replicatedPlayerInstances.Add(replicatedPlayer);

            // Set initial Y rotation offset for Player 3 (index 1) and Player 4 (index 2)
            if (i == 1 || i == 2)
            {
                replicatedPlayer.SetInitialYRotationOffset(180f);
            }
        }
        
        if (clientPlayerPrefab != null && clientPlayerPrefab.GetComponent<ForestIntroPlayer>() != null)
        {
            clientPlayerPrefab.GetComponent<ForestIntroPlayer>().LockCursor();
        }
    }

    /// <summary>
    /// Called by MyNetworkManager to spawn a networked player in the forest scene
    /// </summary>
    public void SpawnNetworkedPlayer(NetworkConnectionToClient conn, GameObject networkPlayerPrefab)
    {
        if (networkPlayerPrefab == null)
        {
            Debug.LogError("ForestPlayerManager: Network player prefab is null.");
            throw new System.ArgumentNullException(nameof(networkPlayerPrefab), "Network player prefab cannot be null");
        }

        if (conn == null)
        {
            Debug.LogError("ForestPlayerManager: Network connection is null.");
            throw new System.ArgumentNullException(nameof(conn), "Network connection cannot be null");
        }

        try
        {
            Debug.Log($"ForestPlayerManager: Starting networked player spawn for connection {conn.connectionId}");

            // Determine spawn point based on connection
            Transform spawnPoint = GetSpawnPointForConnection(conn);
            if (spawnPoint == null)
            {
                string errorMsg = $"ForestPlayerManager: No available spawn point for connection {conn.connectionId}";
                Debug.LogError(errorMsg);
                throw new System.InvalidOperationException(errorMsg);
            }

            // Validate spawn point position
            if (!ValidateSpawnPoint(spawnPoint))
            {
                string errorMsg = $"ForestPlayerManager: Spawn point '{spawnPoint.name}' validation failed for connection {conn.connectionId}";
                Debug.LogError(errorMsg);
                throw new System.InvalidOperationException(errorMsg);
            }

            // Instantiate networked player at spawn point position but without parent to avoid transform calculation issues
            GameObject networkedPlayer = Instantiate(networkPlayerPrefab, spawnPoint.position, spawnPoint.rotation);
            if (networkedPlayer == null)
            {
                string errorMsg = $"ForestPlayerManager: Failed to instantiate networked player for connection {conn.connectionId}";
                Debug.LogError(errorMsg);
                throw new System.InvalidOperationException(errorMsg);
            }

            // Configure the networked player for the forest intro BEFORE network registration
            ConfigureNetworkedPlayerForIntro(networkedPlayer, conn, spawnPoint);

            // Apply proper parenting and transform setup BEFORE network registration to prevent interference
            SetupNetworkedPlayerTransform(networkedPlayer, spawnPoint, conn);

            // Register with Mirror networking
            NetworkServer.AddPlayerForConnection(conn, networkedPlayer);

            // Track the spawned player
            _networkedPlayers.Add(networkedPlayer);

            // Initialize the player through the network manager (after transform setup to prevent NavMeshAgent interference)
            MyNetworkManager.instance.InitializeSpawnedPlayer(conn, networkedPlayer);

            // Mark that we've started spawning networked players
            _hasSpawnedNetworkedPlayers = true;

            Debug.Log($"ForestPlayerManager: Successfully spawned networked player for connection {conn.connectionId} at spawn point '{spawnPoint.name}'");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"ForestPlayerManager: Critical error spawning networked player for connection {conn.connectionId}: {ex.Message}");

            // Clean up any partially created objects
            CleanupFailedSpawn(conn);

            // Re-throw the exception to be handled by the calling code
            throw;
        }
    }

    private Transform GetSpawnPointForConnection(NetworkConnectionToClient conn)
    {
        // For the local server connection (host), use the local player spawn point
        if (conn == NetworkServer.localConnection)
        {
            return localPlayerSpawnPoint;
        }

        // For remote connections, use replicated player spawn points
        int connectionIndex = GetConnectionIndex(conn);

        if (connectionIndex >= 0 && connectionIndex < replicatedPlayerSpawnPoints.Count)
        {
            return replicatedPlayerSpawnPoints[connectionIndex];
        }

        // Fallback to local spawn point if no specific point available
        Debug.LogWarning($"ForestPlayerManager: No specific spawn point for connection {conn.connectionId}, using local spawn point.");
        return localPlayerSpawnPoint;
    }

    private int GetConnectionIndex(NetworkConnectionToClient conn)
    {
        // Simple index assignment based on connection ID
        // In a more sophisticated system, you might track this differently
        return conn.connectionId - 1; // Subtract 1 because host is connection 0
    }

    private void ConfigureNetworkedPlayerForIntro(GameObject networkedPlayer, NetworkConnectionToClient conn, Transform spawnPoint)
    {
        // Ensure the networked player has the necessary components for the intro
        MyClient myClient = networkedPlayer.GetComponent<MyClient>();
        if (myClient == null)
        {
            Debug.LogError("ForestPlayerManager: Networked player missing MyClient component.");
            return;
        }

        // Add ForestIntroPlayer component if not present
        ForestIntroPlayer introPlayer = networkedPlayer.GetComponent<ForestIntroPlayer>();
        if (introPlayer == null)
        {
            introPlayer = networkedPlayer.AddComponent<ForestIntroPlayer>();
        }

        // Configure camera for local player
        if (conn == NetworkServer.localConnection && playerCameraToAssign != null)
        {
            introPlayer.InitializePlayerCamera(playerCameraToAssign);
            _localPlayerInstance = introPlayer;
        }

        // Set the player's name for identification
        networkedPlayer.name = conn == NetworkServer.localConnection ? "NetworkedHost_ForestPlayer" : $"NetworkedClient_ForestPlayer_{conn.connectionId}";

        // Add constraint and immobilization components
        AddConstraintComponent(networkedPlayer);
        AddImmobilizerComponent(networkedPlayer);
    }

    private void SetupNetworkedPlayerTransform(GameObject networkedPlayer, Transform spawnPoint, NetworkConnectionToClient conn)
    {
        if (networkedPlayer != null && spawnPoint != null)
        {
            Debug.Log($"ForestPlayerManager: Setting up networked player transform - Before: World pos {networkedPlayer.transform.position}");

            // Disable physics components during intro to prevent falling
            DisablePhysicsComponents(networkedPlayer);

            // Parent to spawn point and reset local transform to ensure correct local coordinates
            // but preserve the correct scale
            networkedPlayer.transform.SetParent(spawnPoint);
            networkedPlayer.transform.localPosition = Vector3.zero;
            networkedPlayer.transform.localRotation = Quaternion.identity;

            // Enforce correct player scale after all transform operations
            EnforcePlayerScale(networkedPlayer);

            Debug.Log($"ForestPlayerManager: After transform setup - Local pos: {networkedPlayer.transform.localPosition}, World pos: {networkedPlayer.transform.position}");
            Debug.Log($"ForestPlayerManager: Spawn point world pos: {spawnPoint.position}");

            // Add verification for networked players too
            StartCoroutine(VerifyPlayerPositioning(networkedPlayer, spawnPoint));
        }
        else
        {
            Debug.LogError("ForestPlayerManager: Cannot setup transform - networkedPlayer or spawnPoint is null");
        }
    }

    void Update()
    {
        // Handle both networked and non-networked scenarios
        if (MyNetworkManager.isMultiplayer)
        {
            HandleNetworkedPlayerUpdates();
        }
        else
        {
            HandleLocalPlayerUpdates();
        }
    }

    private void HandleNetworkedPlayerUpdates()
    {
        // In multiplayer, the actual player synchronization is handled by Mirror
        // We only need to manage local intro-specific behavior

        if (_localPlayerInstance != null && _networkedPlayers.Count > 1)
        {
            // Get local player orientation for any local intro effects
            Quaternion localPlayerOrientation = _localPlayerInstance.GetWorldOrientation();
            float localPlayerWorldYRotation = localPlayerOrientation.eulerAngles.y;

            // Note: In a real networked scenario, this data would be synchronized
            // through Mirror's networking system rather than direct method calls
        }
    }

    private void HandleLocalPlayerUpdates()
    {
        // This section simulates network updates for single-player mode.
        // In single-player, we simulate multiplayer behavior with replicated players.

        if (_localPlayerInstance != null && _replicatedPlayerInstances.Count > 0)
        {
            // 1. Get the Y rotation (yaw) from the local client player.
            // Using GetWorldOrientation() and then taking .eulerAngles.y to get the yaw component.
            Quaternion localPlayerOrientation = _localPlayerInstance.GetWorldOrientation();
            float localPlayerWorldYRotation = localPlayerOrientation.eulerAngles.y;

            // 2. "Send" this Y rotation to all replicated players.
            foreach (ForestIntroReplicatedPlayer replicatedPlayer in _replicatedPlayerInstances)
            {
                if (replicatedPlayer != null)
                {
                    // Here we directly call the method that uses the Y rotation.
                    // The Replicated Player script itself ensures only its Y world axis is changed.
                    replicatedPlayer.SetVisualWorldYRotation(localPlayerWorldYRotation);
                }
            }
        }
    }

    /// <summary>
    /// Clean up spawned players when the scene is unloaded
    /// </summary>
    private void OnDestroy()
    {
        // Clean up non-networked players
        if (_localPlayerInstance != null && !MyNetworkManager.isMultiplayer)
        {
            Destroy(_localPlayerInstance.gameObject);
        }

        foreach (var replicatedPlayer in _replicatedPlayerInstances)
        {
            if (replicatedPlayer != null && !MyNetworkManager.isMultiplayer)
            {
                Destroy(replicatedPlayer.gameObject);
            }
        }

        // Clear lists
        _replicatedPlayerInstances.Clear();
        _networkedPlayers.Clear();
    }

    /// <summary>
    /// Get the current local player instance (networked or non-networked)
    /// </summary>
    public ForestIntroPlayer GetLocalPlayerInstance()
    {
        return _localPlayerInstance;
    }

    /// <summary>
    /// Check if the manager has spawned any players
    /// </summary>
    public bool HasSpawnedPlayers()
    {
        return _localPlayerInstance != null || _networkedPlayers.Count > 0;
    }

    /// <summary>
    /// Get count of active players in the scene
    /// </summary>
    public int GetActivePlayerCount()
    {
        if (MyNetworkManager.isMultiplayer)
        {
            return _networkedPlayers.Count;
        }
        else
        {
            int count = 0;
            if (_localPlayerInstance != null) count++;
            count += _replicatedPlayerInstances.Count;
            return count;
        }
    }

    /// <summary>
    /// Unified method to spawn any player at a spawn point with proper parent-child setup
    /// </summary>
    private GameObject SpawnPlayerAtPoint(GameObject prefab, Transform spawnPoint, string playerName)
    {
        // Instantiate at spawn point position but without parent to avoid transform calculation issues
        GameObject player = Instantiate(prefab, spawnPoint.position, spawnPoint.rotation);

        Debug.Log($"ForestPlayerManager: Spawning {playerName} - Before parenting: World pos {player.transform.position}");

        // Disable physics components during intro to prevent falling
        DisablePhysicsComponents(player);

        // Immediately parent to spawn point
        player.transform.SetParent(spawnPoint);

        // Reset local transform to ensure exact (0,0,0) positioning relative to spawn point
        // but preserve the correct scale
        player.transform.localPosition = Vector3.zero;
        player.transform.localRotation = Quaternion.identity;

        // Enforce correct player scale after all transform operations
        EnforcePlayerScale(player);

        player.name = playerName;

        Debug.Log($"ForestPlayerManager: After setup - Local pos: {player.transform.localPosition}, World pos: {player.transform.position}");
        Debug.Log($"ForestPlayerManager: Spawn point '{spawnPoint.name}' world pos: {spawnPoint.position}");

        // Add constraint and immobilization components for non-networked players
        AddConstraintComponent(player);
        AddImmobilizerComponent(player);

        // Add a coroutine to double-check positioning after component initialization
        StartCoroutine(VerifyPlayerPositioning(player, spawnPoint));

        return player;
    }

    private System.Collections.IEnumerator VerifyPlayerPositioning(GameObject player, Transform spawnPoint)
    {
        // Wait a frame for all components to initialize
        yield return null;

        Vector3 targetScale = new Vector3(1.56f, 1.56f, 1.56f);
        bool positionChanged = false;
        bool scaleChanged = false;

        // Check if position has been modified by any component
        if (player.transform.localPosition != Vector3.zero)
        {
            Debug.LogWarning($"ForestPlayerManager: Player {player.name} local position was modified to {player.transform.localPosition}. Resetting to (0,0,0).");
            positionChanged = true;
        }

        // Check if scale has been modified by any component
        if (player.transform.localScale != targetScale)
        {
            Debug.LogWarning($"ForestPlayerManager: Player {player.name} scale was modified to {player.transform.localScale}. Resetting to {targetScale}.");
            scaleChanged = true;
        }

        if (positionChanged || scaleChanged)
        {
            // Check if NavMeshAgent is interfering
            UnityEngine.AI.NavMeshAgent navAgent = player.GetComponent<UnityEngine.AI.NavMeshAgent>();
            if (navAgent != null && navAgent.enabled)
            {
                Debug.LogWarning($"ForestPlayerManager: NavMeshAgent on {player.name} is still enabled during intro. Disabling it.");
                navAgent.enabled = false;
            }

            // Reset position and scale
            player.transform.localPosition = Vector3.zero;
            player.transform.localRotation = Quaternion.identity;
            player.transform.localScale = targetScale;
        }

        Debug.Log($"ForestPlayerManager: Final verification - {player.name} Local pos: {player.transform.localPosition}, Scale: {player.transform.localScale}, World pos: {player.transform.position}");
    }

    /// <summary>
    /// Unified method to add constraint component to any player
    /// </summary>
    private void AddConstraintComponent(GameObject player)
    {
        HelicopterTransformConstraint constraint = player.GetComponent<HelicopterTransformConstraint>();
        if (constraint == null)
        {
            constraint = player.AddComponent<HelicopterTransformConstraint>();
        }
    }

    /// <summary>
    /// Unified method to add immobilizer component to any player
    /// </summary>
    private void AddImmobilizerComponent(GameObject player)
    {
        HelicopterPlayerImmobilizer immobilizer = player.GetComponent<HelicopterPlayerImmobilizer>();
        if (immobilizer == null)
        {
            immobilizer = player.AddComponent<HelicopterPlayerImmobilizer>();
            Debug.Log($"ForestPlayerManager: Added HelicopterPlayerImmobilizer to {player.name}");
        }
    }

    /// <summary>
    /// Disables physics components during intro to prevent falling/gravity effects
    /// </summary>
    private void DisablePhysicsComponents(GameObject player)
    {
        // Disable CharacterController to prevent gravity/falling during intro
        CharacterController characterController = player.GetComponent<CharacterController>();
        if (characterController != null)
        {
            characterController.enabled = false;
            Debug.Log($"ForestPlayerManager: Disabled CharacterController for {player.name} during intro");
        }

        // Disable Rigidbody if present
        Rigidbody rb = player.GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.isKinematic = true;
            Debug.Log($"ForestPlayerManager: Set Rigidbody to kinematic for {player.name} during intro");
        }

        // Disable NavMeshAgent to prevent position interference during intro
        UnityEngine.AI.NavMeshAgent navMeshAgent = player.GetComponent<UnityEngine.AI.NavMeshAgent>();
        if (navMeshAgent != null)
        {
            navMeshAgent.enabled = false;
            Debug.Log($"ForestPlayerManager: Disabled NavMeshAgent for {player.name} during intro");
        }

        // Disable ForestPlayer movement component if present
        ForestPlayer forestPlayer = player.GetComponent<ForestPlayer>();
        if (forestPlayer != null)
        {
            forestPlayer.enabled = false;
            Debug.Log($"ForestPlayerManager: Disabled ForestPlayer movement for {player.name} during intro");
        }

        // Disable Movement component if present
        Movement movement = player.GetComponent<Movement>();
        if (movement != null)
        {
            movement.enabled = false;
            Debug.Log($"ForestPlayerManager: Disabled Movement component for {player.name} during intro");
        }
    }

    /// <summary>
    /// Enforces the correct player scale of 1.56 on all axes
    /// </summary>
    private void EnforcePlayerScale(GameObject player)
    {
        if (player == null)
        {
            Debug.LogError("ForestPlayerManager: Cannot enforce scale on null player object.");
            return;
        }

        Vector3 targetScale = new Vector3(1.56f, 1.56f, 1.56f);
        player.transform.localScale = targetScale;

        Debug.Log($"ForestPlayerManager: Enforced player scale to {targetScale} for {player.name}");
    }

    /// <summary>
    /// Validates that a spawn point is suitable for player spawning
    /// </summary>
    private bool ValidateSpawnPoint(Transform spawnPoint)
    {
        if (spawnPoint == null)
        {
            Debug.LogError("ForestPlayerManager: Spawn point is null");
            return false;
        }

        // Check if spawn point position is valid
        Vector3 spawnPosition = spawnPoint.position;

        // Validate position is not at origin (likely indicates uninitialized spawn point)
        if (spawnPosition == Vector3.zero)
        {
            Debug.LogWarning($"ForestPlayerManager: Spawn point '{spawnPoint.name}' is at world origin, which may indicate improper setup");
        }

        // Check if spawn point is within reasonable bounds
        float maxDistance = 1000f; // Reasonable world bounds
        if (spawnPosition.magnitude > maxDistance)
        {
            Debug.LogError($"ForestPlayerManager: Spawn point '{spawnPoint.name}' is too far from origin ({spawnPosition.magnitude:F2} units)");
            return false;
        }

        // Check if spawn point has reasonable Y coordinate (not underground or too high)
        if (spawnPosition.y < -100f || spawnPosition.y > 1000f)
        {
            Debug.LogError($"ForestPlayerManager: Spawn point '{spawnPoint.name}' has invalid Y coordinate ({spawnPosition.y:F2})");
            return false;
        }

        return true;
    }

    /// <summary>
    /// Cleans up any objects created during a failed spawn attempt
    /// </summary>
    private void CleanupFailedSpawn(NetworkConnectionToClient conn)
    {
        try
        {
            // Remove any partially created networked players for this connection
            for (int i = _networkedPlayers.Count - 1; i >= 0; i--)
            {
                GameObject player = _networkedPlayers[i];
                if (player != null)
                {
                    // Check if this player belongs to the failed connection
                    MyClient client = player.GetComponent<MyClient>();
                    if (client != null && client.connectionToClient == conn)
                    {
                        Debug.Log($"ForestPlayerManager: Cleaning up failed spawn for connection {conn.connectionId}");
                        _networkedPlayers.RemoveAt(i);

                        // Destroy the GameObject
                        if (NetworkServer.active)
                        {
                            NetworkServer.Destroy(player);
                        }
                        else
                        {
                            Destroy(player);
                        }
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"ForestPlayerManager: Error during cleanup for connection {conn.connectionId}: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the overall state of the ForestPlayerManager
    /// </summary>
    public bool ValidateManagerState()
    {
        bool isValid = true;

        // Check essential prefab references
        if (clientPlayerPrefab == null)
        {
            Debug.LogError("ForestPlayerManager: clientPlayerPrefab is not assigned");
            isValid = false;
        }

        // Check spawn point configuration
        if (localPlayerSpawnPoint == null)
        {
            Debug.LogError("ForestPlayerManager: localPlayerSpawnPoint is not assigned");
            isValid = false;
        }

        if (replicatedPlayerSpawnPoints == null || replicatedPlayerSpawnPoints.Count == 0)
        {
            Debug.LogWarning("ForestPlayerManager: No replicated player spawn points configured");
        }

        // Validate spawn points
        if (localPlayerSpawnPoint != null && !ValidateSpawnPoint(localPlayerSpawnPoint))
        {
            Debug.LogError("ForestPlayerManager: Local player spawn point validation failed");
            isValid = false;
        }

        for (int i = 0; i < replicatedPlayerSpawnPoints.Count; i++)
        {
            if (replicatedPlayerSpawnPoints[i] != null && !ValidateSpawnPoint(replicatedPlayerSpawnPoints[i]))
            {
                Debug.LogError($"ForestPlayerManager: Replicated player spawn point {i} validation failed");
                isValid = false;
            }
        }

        return isValid;
    }

    /// <summary>
    /// Gets detailed status information for debugging
    /// </summary>
    public string GetStatusInfo()
    {
        System.Text.StringBuilder status = new System.Text.StringBuilder();
        status.AppendLine("=== ForestPlayerManager Status ===");
        status.AppendLine($"Multiplayer Mode: {MyNetworkManager.isMultiplayer}");
        status.AppendLine($"Has Spawned Networked Players: {_hasSpawnedNetworkedPlayers}");
        status.AppendLine($"Local Player Instance: {(_localPlayerInstance != null ? _localPlayerInstance.name : "None")}");
        status.AppendLine($"Networked Players Count: {_networkedPlayers.Count}");
        status.AppendLine($"Replicated Players Count: {_replicatedPlayerInstances.Count}");
        status.AppendLine($"Available Spawn Points: {replicatedPlayerSpawnPoints.Count}");

        // List networked players
        if (_networkedPlayers.Count > 0)
        {
            status.AppendLine("Networked Players:");
            for (int i = 0; i < _networkedPlayers.Count; i++)
            {
                GameObject player = _networkedPlayers[i];
                if (player != null)
                {
                    MyClient client = player.GetComponent<MyClient>();
                    int connectionId = client?.connectionToClient?.connectionId ?? -1;
                    status.AppendLine($"  [{i}] {player.name} (Connection: {connectionId})");
                }
                else
                {
                    status.AppendLine($"  [{i}] NULL PLAYER");
                }
            }
        }

        return status.ToString();
    }
}
