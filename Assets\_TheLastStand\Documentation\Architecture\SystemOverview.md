# System Overview - TheLastStand

## 🏗️ High-Level Architecture

TheLastStand is a multiplayer survival game built on Unity with Mirror networking and Steam integration. The architecture follows a modular design with clear separation of concerns.

```mermaid
graph TB
    subgraph "Client Layer"
        UI[UI Systems]
        Input[Input Handling]
        Rendering[Rendering & Audio]
    end
    
    subgraph "Game Logic Layer"
        Player[Player Management]
        Scene[Scene Management]
        Game[Game Systems]
    end
    
    subgraph "Network Layer"
        Mirror[Mirror Networking]
        Steam[Steam Integration]
        Lobby[Lobby Management]
    end
    
    subgraph "Core Layer"
        Managers[Core Managers]
        Utils[Utilities & Tools]
        Data[Persistent Data]
    end
    
    UI --> Player
    Input --> Player
    Player --> Mirror
    Scene --> Mirror
    Game --> Mirror
    Mirror --> Steam
    Lobby --> Steam
    Player --> Managers
    Scene --> Managers
    Game --> Managers
```

## 🎯 Core Design Principles

### 1. **Singleton Pattern for Managers**
- `MyNetworkManager.instance` - Network management
- `MainMenu.instance` - Main menu state
- `SteamLobby.instance` - Steam lobby operations
- `GameManager.Instance` - Core game management

### 2. **Scene-Specific Management**
- `ForestGameManager` - Forest map specific logic
- `ForestPlayerManager` - Forest player spawning and intro
- `HelicopterImmobilizationManager` - Player immobilization during helicopter sequences
- `MainMenu_AudioManager` - Main menu audio management

### 3. **Modular System Design**
- **Core Systems** - Essential game functionality
- **Player Systems** - Player-related components
- **Networking** - Multiplayer and Steam integration
- **UI Systems** - User interface management
- **Game Systems** - Scene-specific gameplay

## 🔄 System Flow

### Game Startup Flow
```mermaid
sequenceDiagram
    participant Unity
    participant Steam
    participant Network
    participant MainMenu
    
    Unity->>Steam: Initialize SteamManager
    Steam->>Network: Setup MyNetworkManager
    Network->>MainMenu: Load MainMenu scene
    MainMenu->>MainMenu: Initialize UI systems
```

### Multiplayer Session Flow
```mermaid
sequenceDiagram
    participant Host
    participant Steam
    participant Client
    participant Game
    
    Host->>Steam: Create lobby
    Steam->>Host: Lobby created
    Host->>Network: Start host
    Client->>Steam: Join lobby
    Steam->>Client: Lobby joined
    Client->>Network: Connect to host
    Network->>Game: Load Map_01 scene
    Game->>Game: Spawn players
```

## 📁 Folder Structure

```
Assets/_TheLastStand/
├── Scripts/
│   ├── Core/                 # Core game systems
│   ├── Player/               # Player-related components
│   ├── Networking/           # Network and lobby management
│   ├── ForestMap/            # Forest map specific systems
│   ├── MainMenuHelpers/      # Main menu functionality
│   ├── UI/                   # User interface systems
│   ├── Steamworks.NET/       # Steam integration
│   ├── Tools/                # Development tools
│   └── ParentClasses/        # Base classes
├── Scenes/                   # Game scenes
├── Prefabs/                  # Reusable game objects
├── Materials/                # Rendering materials
└── Documentation/            # Project documentation
```

## 🔗 System Dependencies

### Core Dependencies
- **Unity 2022.3+** - Game engine
- **Mirror Networking** - Multiplayer framework
- **Steamworks.NET** - Steam integration
- **TextMeshPro** - UI text rendering

### Internal Dependencies
```mermaid
graph LR
    MyNetworkManager --> LobbyPlayerList
    MyNetworkManager --> SteamLobby
    MyClient --> CharacterSkinHandler
    ForestPlayerManager --> MyNetworkManager
    MainMenu --> SteamLobby
    MainMenu --> LobbyController
```

## 🎮 Key Systems

### 1. **Networking System**
- **MyNetworkManager** - Central network management
- **LobbyController** - Game session control
- **LobbyPlayerList** - Player list management

### 2. **Player System**
- **MyClient** - Network player representation
- **ForestIntroPlayer** - Forest scene player controller
- **HelicopterPlayerImmobilizer** - Complete player immobilization during helicopter sequences
- **CharacterSkinHandler** - Player appearance management

### 3. **Steam Integration**
- **SteamManager** - Steam API initialization
- **SteamLobby** - Lobby creation and management
- **SteamHelper** - Steam utility functions

### 4. **Scene Management**
- **GameManager** - Base game management
- **ForestGameManager** - Forest scene management
- **Scene transitions** - MainMenu ↔ Map_01

### 5. **Helicopter Systems**
- **HelicopterConstraintManager** - Position and rotation constraints during flight
- **HelicopterImmobilizationManager** - Complete movement and physics disabling
- **HelicopterExitManager** - Coordinated player exit sequences

## 🔧 Configuration

### Network Configuration
- **Max Connections**: 4 players
- **Transport**: Mirror's default transport
- **Scene Management**: Automatic scene switching

### Steam Configuration
- **Lobby Type**: Public lobbies
- **Matchmaking**: Distance-based filtering
- **Authentication**: Steam ID validation

## 📊 Performance Considerations

### Network Optimization
- **SyncVar** usage minimization
- **RPC** batching where possible
- **NetworkPerformanceMonitor** for monitoring

### Memory Management
- **Object pooling** for frequently spawned objects
- **Proper cleanup** in OnDestroy methods
- **Scene-specific resource loading**

---

**Next:** [Networking Architecture](./NetworkingArchitecture.md) | [Player Management](./PlayerManagement.md)
