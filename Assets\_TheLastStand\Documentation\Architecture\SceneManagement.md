# Scene Management - TheLastStand

## 🎬 Overview

TheLastStand uses a structured scene management system that handles transitions between MainMenu and gameplay scenes, with specialized managers for each scene type and coordinated multiplayer scene loading.

## 🏗️ Scene Architecture

```mermaid
graph TB
    subgraph "Scene Flow"
        MainMenu[MainMenu Scene]
        Map01[Map_01 Scene]
        MainMenu --> Map01
        Map01 --> MainMenu
    end
    
    subgraph "MainMenu Components"
        MainMenuUI[Main Menu UI]
        SteamLobby[Steam Lobby]
        LobbyController[Lobby Controller]
        AudioManager1[MainMenu Audio Manager]
    end
    
    subgraph "Map_01 Components"
        ForestGameManager[Forest Game Manager]
        ForestPlayerManager[Forest Player Manager]
        ForestIntroHelicopter[Forest Intro Helicopter]
        AudioManager2[ForestMap Audio Manager]
    end
    
    MainMenu --> MainMenuUI
    MainMenu --> SteamLobby
    MainMenu --> LobbyController
    MainMenu --> AudioManager1
    
    Map01 --> ForestGameManager
    Map01 --> ForestPlayerManager
    Map01 --> ForestIntroHelicopter
    Map01 --> AudioManager2
```

## 🎮 Scene Definitions

### MainMenu Scene
**Location:** `Assets/_TheLastStand/Scenes/MainMenu.unity`

The main menu and lobby management scene.

**Key Components:**
- **MainMenu** - UI state management and player interactions
- **SteamLobby** - Steam lobby creation and joining
- **LobbyController** - Game session control
- **MainMenu_AudioManager** - Menu-specific audio
- **PopupManager** - Modal dialogs and notifications

**Scene Purpose:**
- Player authentication via Steam
- Lobby creation and matchmaking
- Player ready state management
- Game session initiation

### Map_01 Scene
**Location:** `Assets/_TheLastStand/Scenes/Map_01.unity`

The forest gameplay scene with intro sequence.

**Key Components:**
- **ForestGameManager** - Scene-specific game logic
- **ForestPlayerManager** - Player spawning and intro coordination
- **ForestIntroHelicopter** - Helicopter intro sequence
- **ForestMap_AudioManager** - Forest-specific audio
- **ForestMap_UIManager** - In-game UI management

**Scene Purpose:**
- Helicopter intro sequence
- Multiplayer gameplay
- Player spawning and management
- Forest environment interaction

## 🔄 Scene Transition Flow

### MainMenu → Map_01 Transition

```mermaid
sequenceDiagram
    participant Player
    participant MainMenu
    participant LobbyController
    participant MyNetworkManager
    participant AllClients
    participant ForestGameManager
    
    Player->>MainMenu: Click "Start Game"
    MainMenu->>LobbyController: StartGameWithParty()
    LobbyController->>LobbyController: Check AllPlayersReady()
    
    alt All Players Ready
        LobbyController->>MyNetworkManager: ServerChangeScene("Map_01")
        MyNetworkManager->>AllClients: Load Map_01
        AllClients->>ForestGameManager: Scene loaded
        ForestGameManager->>ForestGameManager: Start intro sequence
    else Players Not Ready
        LobbyController->>Player: Wait for ready state
    end
```

### Scene Loading Process

1. **Pre-Transition Cleanup**
   ```csharp
   // In LobbyController.StartGame()
   if (LobbyPlayerList.instance != null)
   {
       foreach (var client in LobbyPlayerList.instance.allClients)
       {
           if (client != null)
           {
               client.IsReady = false; // Reset ready state
           }
       }
   }
   
   if (MainMenu.instance != null)
   {
       Destroy(MainMenu.instance.gameObject);
   }
   ```

2. **Network Scene Change**
   ```csharp
   NetworkManager.singleton.ServerChangeScene("Map_01");
   ```

3. **Post-Load Initialization**
   ```csharp
   // In ForestGameManager.Start()
   forestIntroHelicopter.Invoke("StartIntroSequence", 1f);
   forestMapAudioManager.PlayIntroStormSound();
   forestMapAudioManager.PlayIntroBackgroundMusic();
   ```

## 🎯 Scene-Specific Managers

### GameManager (Base Class)
**Location:** `Assets/_TheLastStand/Scripts/ParentClasses/GameManager.cs`

Base class for all scene-specific game managers.

```csharp
public class GameManager : MonoBehaviour
{
    public static GameManager Instance;
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
        }
    }
    
    public void QuitGame()
    {
        Application.Quit();
    }
}
```

### ForestGameManager
**Location:** `Assets/_TheLastStand/Scripts/Core/ForestGameManager.cs`

Manages the forest scene initialization and intro sequence.

**Key Responsibilities:**
- Helicopter intro sequence coordination
- Audio system initialization
- Scene-specific system startup
- Networking state logging

**Initialization Sequence:**
```csharp
public void Start()
{
    // Ensure ForestPlayerManager reference
    if (forestPlayerManager == null)
    {
        forestPlayerManager = FindObjectOfType<ForestPlayerManager>();
    }

    // Start intro sequence
    if (forestIntroHelicopter != null)
    {
        forestIntroHelicopter.Invoke("StartIntroSequence", 1f);
    }

    // Initialize audio
    if (forestMapAudioManager != null)
    {
        forestMapAudioManager.PlayIntroStormSound();
        forestMapAudioManager.PlayIntroBackgroundMusic();
        forestMapAudioManager.Invoke("PlayNarratorIntro", 2f);
    }
}
```

## 🎵 Audio Management

### Scene-Specific Audio Managers

Each scene has its own audio manager for appropriate soundscapes:

**MainMenu_AudioManager:**
- Main menu background music
- UI sound effects
- Volume control integration

**ForestMap_AudioManager:**
- Intro storm sounds
- Background forest ambience
- Narrator voice lines
- Helicopter audio

### Audio Transition Handling
```csharp
// In ForestGameManager.OnHelicopterLanded()
public void OnHelicopterLanded()
{
    // Fade out helicopter sound
    AudioSettings.Instance.FadeOutSound(
        forestIntroHelicopter.GetComponent<AudioSource>(), 
        2f
    );
}
```

## 🔧 Scene Configuration

### Network Scene Management
```csharp
// In MyNetworkManager
public override void OnServerAddPlayer(NetworkConnectionToClient conn)
{
    string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
    
    if (currentScene == "Map_01")
    {
        HandleForestMapSpawning(conn);
    }
    else
    {
        HandleDefaultSpawning(conn);
    }
}
```

### Scene-Specific Player Behavior
```csharp
// In MyClient.Start()
string currentScene = SceneManager.GetActiveScene().name;

if (currentScene != "MainMenu")
{
    ToggleController(true);
    
    if (currentScene == "Map_01")
    {
        HandleForestIntroSetup();
    }
}
else
{
    ToggleController(false);
}
```

## 🎬 Intro Sequence System

### Helicopter Intro Flow
```mermaid
sequenceDiagram
    participant ForestGameManager
    participant ForestIntroHelicopter
    participant ImmobilizationManager
    participant ForestMap_AudioManager
    participant Players

    ForestGameManager->>ForestIntroHelicopter: StartIntroSequence()
    ForestGameManager->>ForestMap_AudioManager: PlayIntroStormSound()
    ForestGameManager->>ForestMap_AudioManager: PlayIntroBackgroundMusic()
    ForestMap_AudioManager->>ForestMap_AudioManager: PlayNarratorIntro() (delayed)
    ForestIntroHelicopter->>ImmobilizationManager: Helicopter state change (MovingToWaypoint)
    ImmobilizationManager->>Players: Activate complete immobilization
    ForestIntroHelicopter->>Players: Helicopter animation (players locked in position)
    ForestIntroHelicopter->>ImmobilizationManager: Helicopter state change (Landing)
    ImmobilizationManager->>Players: Deactivate immobilization
    ForestIntroHelicopter->>ForestGameManager: OnHelicopterLanded()
    ForestGameManager->>ForestMap_AudioManager: FadeOutSound()
```

### Helicopter Component
**Location:** `Assets/_TheLastStand/Scripts/ForestMap/ForestIntroHelicopter.cs`

Extends the base Helicopter class for forest-specific intro behavior.

```csharp
public class ForestIntroHelicopter : Helicopter
{
    [Header("Forest Intro Specific Settings")]
    [SerializeField] private float specificLandingTime = 3f;

    public void StartIntroSequence()
    {
        if (waypoints?.Count > 0)
        {
            currentTargetWaypointTransform = waypoints[0];
            BeginNavigation(waypoints, specificLandingTime);
        }
    }
}
```

### Player Immobilization System
**Location:** `Assets/_TheLastStand/Scripts/ForestMap/HelicopterImmobilizationManager.cs`

Manages complete player immobilization during helicopter intro sequences.

**Key Features:**
- **Complete Movement Disabling**: Disables all movement components (Movement.cs, ForestPlayer.cs)
- **Physics Disabling**: Disables CharacterController, NavMeshAgent, Rigidbody physics
- **Position Locking**: Continuously enforces local position (0,0,0) relative to spawn points
- **Input Blocking**: Prevents movement input (optionally camera input)
- **Network Synchronization**: Synchronized immobilization state across all clients

**Integration:**
```csharp
// Automatic activation during helicopter flight states
case HelicopterState.InitializingPath:
case HelicopterState.MovingToWaypoint:
case HelicopterState.Rotating:
    ActivateImmobilization();
    break;

case HelicopterState.Landing:
case HelicopterState.PathComplete:
    DeactivateImmobilization();
    break;
```

## 🔄 Scene Persistence

### Persistent Objects
Some objects persist across scene changes:
- **MyNetworkManager** - DontDestroyOnLoad
- **SteamManager** - DontDestroyOnLoad
- **AudioSettings** - Persistent data

### Scene-Specific Cleanup
```csharp
// In ForestPlayerManager.OnDestroy()
private void OnDestroy()
{
    // Clean up non-networked players
    if (_localPlayerInstance != null && !MyNetworkManager.isMultiplayer)
    {
        Destroy(_localPlayerInstance.gameObject);
    }

    foreach (var replicatedPlayer in _replicatedPlayerInstances)
    {
        if (replicatedPlayer != null && !MyNetworkManager.isMultiplayer)
        {
            Destroy(replicatedPlayer.gameObject);
        }
    }

    _replicatedPlayerInstances.Clear();
    _networkedPlayers.Clear();
}
```

## 🐛 Common Issues

### Scene Transition Problems
- **Players not spawning** → Check ForestPlayerManager setup
- **Audio not playing** → Verify audio manager references
- **UI not responding** → Check scene-specific UI managers
- **Players not immobilized during helicopter** → Check HelicopterImmobilizationManager setup

### Timing Issues
- **Intro sequence starting too early** → Adjust delay timers
- **Network players not ready** → Check initialization order
- **Audio overlap** → Implement proper audio transitions

### Performance Issues
- **Long loading times** → Optimize scene assets
- **Memory leaks** → Ensure proper cleanup
- **Network desync** → Verify scene loading completion

---

**Next:** [Steam Integration](../GameSystems/SteamIntegration.md) | [API Reference](../API/CoreSystems.md)
