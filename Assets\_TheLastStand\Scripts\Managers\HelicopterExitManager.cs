using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Mirror;
using UnityEngine;

public class HelicopterExitManager : NetworkBehaviour
{
    public static HelicopterExitManager Instance { get; private set; }

    [Header("Exit Configuration")]
    [SerializeField] private Transform helicopterExitWaypoint;
    [SerializeField] private float exitDelaySeconds = 1.0f;
    [SerializeField] private List<int> playerExitOrder = new List<int> { 2, 4, 1, 3 };

    private List<ForestPlayer> playersToExit = new List<ForestPlayer>();
    private HashSet<ForestPlayer> arrivedPlayers = new HashSet<ForestPlayer>();
    private bool exitSequenceInProgress = false;

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        else if (Instance != this)
        {
            Debug.LogWarning("Multiple HelicopterExitManager instances detected. Destroying duplicate.");
            Destroy(gameObject);
            return;
        }
    }

    [Server]
    public void BeginHelicopterExitSequence()
    {
        if (!isServer)
        {
            Debug.LogError("BeginHelicopterExitSequence can only be called on the server");
            return;
        }

        if (exitSequenceInProgress)
        {
            Debug.LogWarning("Exit sequence already in progress!");
            return;
        }

        if (helicopterExitWaypoint == null)
        {
            Debug.LogError("HelicopterExitWaypoint not assigned!");
            return;
        }

        Debug.Log("Beginning helicopter exit sequence");
        exitSequenceInProgress = true;

        // Notify constraint manager about exit sequence start
        HelicopterConstraintManager constraintManager = HelicopterConstraintManager.Instance;
        if (constraintManager != null)
        {
            constraintManager.OnHelicopterExitSequenceStarted();
        }

        // Notify immobilization manager about exit sequence start
        HelicopterImmobilizationManager immobilizationManager = HelicopterImmobilizationManager.Instance;
        if (immobilizationManager != null)
        {
            immobilizationManager.OnHelicopterExitSequenceStarted();
        }

        // Find all forest players in the scene
        FindPlayersAndStartExitSequence();
    }

    [Server]
    private void FindPlayersAndStartExitSequence()
    {
        // Clear previous state
        playersToExit.Clear();
        arrivedPlayers.Clear();

        // Find all players
        ForestPlayer[] allPlayers = FindObjectsOfType<ForestPlayer>();
        foreach (ForestPlayer player in allPlayers)
        {
            if (player == null || !player.isActiveAndEnabled)
                continue;

            playersToExit.Add(player);
            Debug.Log($"Added player {player.netId} to exit sequence");
        }

        if (playersToExit.Count == 0)
        {
            Debug.LogWarning("No players found for exit sequence!");
            exitSequenceInProgress = false;
            return;
        }

        // Start the exit sequence
        StartCoroutine(Server_InitiatePlayerExit());
    }

    [Server]
    private IEnumerator Server_InitiatePlayerExit()
    {
        // Sort players by the specified order
        List<ForestPlayer> sortedPlayers = new List<ForestPlayer>();

        // First, create a dictionary to look up players by their slot ID
        Dictionary<int, ForestPlayer> playersBySlotId = new Dictionary<int, ForestPlayer>();
        foreach (ForestPlayer player in playersToExit)
        {
            PlayerIdentity identity = player.GetComponent<PlayerIdentity>();
            if (identity != null)
            {
                int slotId = identity.PlayerSlotId;
                playersBySlotId[slotId] = player;
                Debug.Log($"Player {player.netId} has slot ID {slotId}");
            }
            else
            {
                Debug.LogWarning($"Player {player.netId} does not have a PlayerIdentity component!");
            }
        }

        // Then, order players according to the specified exit order
        foreach (int slotId in playerExitOrder)
        {
            if (playersBySlotId.TryGetValue(slotId, out ForestPlayer player))
            {
                sortedPlayers.Add(player);
                Debug.Log($"Added player with slot ID {slotId} to sorted exit order");
            }
            else
            {
                Debug.LogWarning($"No player found with slot ID {slotId} for exit sequence!");
            }
        }

        // Add any remaining players not specified in the order
        foreach (ForestPlayer player in playersToExit)
        {
            PlayerIdentity identity = player.GetComponent<PlayerIdentity>();
            if (identity != null && !sortedPlayers.Contains(player))
            {
                sortedPlayers.Add(player);
                Debug.Log($"Added player with slot ID {identity.PlayerSlotId} to end of sorted exit order (not in specified order)");
            }
        }

        // Send each player to the exit waypoint with the specified delay
        for (int i = 0; i < sortedPlayers.Count; i++)
        {
            ForestPlayer player = sortedPlayers[i];
            if (player == null || !player.isActiveAndEnabled)
                continue;

            Debug.Log($"Initiating AI exit for player {player.netId}");
            
            // Set the player to AI control mode
            player.isAiControlled = true;
            
            // Tell the player where to go
            player.TargetRpc_StartAiExit(player.connectionToClient, helicopterExitWaypoint.position);
            
            // Wait before sending the next player
            if (i < sortedPlayers.Count - 1)
                yield return new WaitForSeconds(exitDelaySeconds);
        }
    }

    [Server]
    public void Server_PlayerReportedArrival(ForestPlayer player)
    {
        if (!exitSequenceInProgress || player == null)
            return;

        if (!playersToExit.Contains(player))
        {
            Debug.LogWarning($"Player {player.netId} reported arrival but is not in the exit sequence!");
            return;
        }

        Debug.Log($"Player {player.netId} arrived at exit waypoint");
        arrivedPlayers.Add(player);

        // Check if all players have arrived
        if (arrivedPlayers.Count >= playersToExit.Count)
        {
            Debug.Log("All players have arrived at exit waypoint");
            StartCoroutine(Server_AllPlayersExited());
        }
    }

    [Server]
    private IEnumerator Server_AllPlayersExited()
    {
        Debug.Log("All players have exited the helicopter, returning control");

        // Small delay before returning control
        yield return new WaitForSeconds(0.5f);

        foreach (ForestPlayer player in playersToExit)
        {
            if (player == null || !player.isActiveAndEnabled)
                continue;

            // Return control to the player
            player.isAiControlled = false;
            player.TargetRpc_EndAiExit(player.connectionToClient);
            Debug.Log($"Returned control to player {player.netId}");
        }

        // Reset for potential future use
        exitSequenceInProgress = false;
        playersToExit.Clear();
        arrivedPlayers.Clear();
    }
} 