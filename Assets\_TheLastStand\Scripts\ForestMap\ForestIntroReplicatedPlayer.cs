using UnityEngine;

public class ForestIntroReplicatedPlayer : <PERSON>o<PERSON><PERSON><PERSON><PERSON>
{
    [Header("References")]
    [Toolt<PERSON>("The transform of the replicated player's visual model. This is what will be rotated.")]
    [SerializeField] private Transform playerModel;

    private float initialYRotationOffset = 0f;

    void Awake()
    {
        if (playerModel == null)
        {
            // If not assigned, assume the model is this transform or a child named "Model"
            Transform modelChild = transform.Find("Model");
            if (modelChild != null)
            {
                playerModel = modelChild;
            }
            else
            {
                playerModel = transform; // Default to this object's transform
            }
            Debug.LogWarning($"ForestIntroReplicatedPlayer on {gameObject.name}: 'playerModel' was not explicitly assigned. Defaulted to {(playerModel == transform ? "this object\'s transform" : "child \'Model\'.")}", this);
        }
    }

    public void SetInitialYRotationOffset(float offset)
    {
        this.initialYRotationOffset = offset;
    }

    /// <summary>
    /// Updates the replicated player's visual model to face a new world Y rotation,
    /// while keeping its world X and Z rotations unchanged.
    /// </summary>
    /// <param name="newWorldAngle">The target world Y rotation in degrees.</param>
    public void SetVisualWorldYRotation(float newWorldAngle)
    {
        if (playerModel != null)
        {
            // Apply yaw rotation to Y-axis to keep player upright, with initial offset
            playerModel.rotation = Quaternion.Euler(0f, initialYRotationOffset + newWorldAngle, 0f);
        }
        else
        {
            Debug.LogError("ForestIntroReplicatedPlayer: 'playerModel' is null. Cannot update rotation.", this);
        }
    }

    /// <summary>
    /// Updates the replicated player's visual model based on a full world orientation,
    /// but only applies the Y component (yaw) to its rotation, keeping world X and Z unchanged.
    /// </summary>
    /// <param name="newWorldOrientation">The target full world orientation.</param>
    public void SetVisualWorldYRotation(Quaternion newWorldOrientation)
    {
        SetVisualWorldYRotation(newWorldOrientation.eulerAngles.y); // Use YAW from the quaternion for Y rotation visual
    }

    /// <summary>
    /// Legacy method name for backward compatibility. Use SetVisualWorldYRotation instead.
    /// </summary>
    /// <param name="newWorldAngle">The target world Y rotation in degrees.</param>
    [System.Obsolete("Use SetVisualWorldYRotation instead. This method name was misleading.")]
    public void SetVisualWorldZRotation(float newWorldAngle)
    {
        SetVisualWorldYRotation(newWorldAngle);
    }

    /// <summary>
    /// Legacy method name for backward compatibility. Use SetVisualWorldYRotation instead.
    /// </summary>
    /// <param name="newWorldOrientation">The target full world orientation.</param>
    [System.Obsolete("Use SetVisualWorldYRotation instead. This method name was misleading.")]
    public void SetVisualWorldZRotation(Quaternion newWorldOrientation)
    {
        SetVisualWorldYRotation(newWorldOrientation);
    }
}
