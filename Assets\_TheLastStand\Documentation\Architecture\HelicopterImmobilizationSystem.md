# Helicopter Immobilization System

## Overview

The Helicopter Immobilization System provides complete player immobilization during helicopter intro sequences in the Forest Map scene. It ensures players remain completely stationary and locked in position until the helicopter landing sequence completes, creating a cinematic helicopter ride experience.

## Architecture

### Core Components

#### HelicopterImmobilizationManager
**Location:** `Assets/_TheLastStand/Scripts/ForestMap/HelicopterImmobilizationManager.cs`

Central manager that coordinates immobilization for all players based on helicopter flight state.

**Key Features:**
- Automatic immobilization activation during helicopter flight
- Integration with helicopter state machine
- Centralized player registration and management
- Network-synchronized global immobilization state

**State Management:**
- **Flight States** → Immobilization Active
- **Landing/PathComplete** → Immobilization Deactivated
- **Exit Sequence** → Immobilization Deactivated

#### HelicopterPlayerImmobilizer
**Location:** `Assets/_TheLastStand/Scripts/ForestMap/HelicopterPlayerImmobilizer.cs`

Per-player component that handles complete immobilization including movement, input, and physics disabling.

**Key Features:**
- Complete movement system disabling
- Physics and gravity disabling
- Input blocking (movement and optionally camera)
- Position locking to spawn point local coordinates
- Component state tracking and restoration

## Integration Points

### ForestPlayerManager Integration
Automatically adds `HelicopterPlayerImmobilizer` components to all spawned players (both networked and non-networked):

```csharp
// In ConfigureNetworkedPlayerForIntro() and SpawnPlayerAtPoint()
AddImmobilizerComponent(player);
```

### Movement System Integration
All movement components check immobilization state before processing input:

**Movement.cs:**
```csharp
HelicopterPlayerImmobilizer immobilizer = GetComponent<HelicopterPlayerImmobilizer>();
if (immobilizer != null && immobilizer.ShouldBlockMovementInput())
{
    return; // Skip all movement processing
}
```

**ForestPlayer.cs:**
```csharp
HelicopterPlayerImmobilizer immobilizer = GetComponent<HelicopterPlayerImmobilizer>();
if (immobilizer != null && immobilizer.ShouldBlockMovementInput())
{
    return; // Skip movement and look processing
}
```

**ForestIntroPlayer.cs:**
```csharp
HelicopterPlayerImmobilizer immobilizer = GetComponent<HelicopterPlayerImmobilizer>();
if (immobilizer != null && immobilizer.ShouldBlockCameraInput())
{
    return; // Skip input processing
}
```

### Helicopter State Machine Integration
Automatically responds to helicopter state changes:

```csharp
// In HelicopterImmobilizationManager.HandleHelicopterStateChange()
switch (newState)
{
    case HelicopterState.InitializingPath:
    case HelicopterState.MovingToWaypoint:
    case HelicopterState.Rotating:
        ActivateImmobilization();
        break;
    case HelicopterState.Landing:
    case HelicopterState.PathComplete:
        DeactivateImmobilization();
        break;
}
```

### Exit Manager Integration
Coordinates with HelicopterExitManager for proper timing:

```csharp
// In HelicopterExitManager.BeginHelicopterExitSequence()
HelicopterImmobilizationManager immobilizationManager = HelicopterImmobilizationManager.Instance;
if (immobilizationManager != null)
{
    immobilizationManager.OnHelicopterExitSequenceStarted();
}
```

## Configuration

### Global Immobilization Settings (HelicopterImmobilizationManager)
- **Enable Immobilization During Flight**: Automatically activate during helicopter movement
- **Disable Immobilization On Landing**: Automatically deactivate when helicopter lands
- **Disable Immobilization On Exit**: Automatically deactivate when exit sequence begins
- **Activation/Deactivation Delays**: Timing controls for smooth transitions

### Per-Player Immobilization Settings (HelicopterPlayerImmobilizer)
- **Locked Local Position**: Target position relative to spawn point (default: 0,0,0)
- **Lock Rotation**: Whether to lock player rotation
- **Disable Physics**: Whether to disable gravity and physics
- **Disable Movement Input**: Whether to block movement input
- **Disable Camera Input**: Whether to block camera input (default: false for cinematic experience)

## Usage Examples

### Manual Immobilization Control
```csharp
// Get immobilization manager
HelicopterImmobilizationManager manager = HelicopterImmobilizationManager.Instance;

// Force activate immobilization for all players
manager.ForceActivateImmobilization();

// Force deactivate immobilization for all players
manager.ForceDeactivateImmobilization();

// Check immobilization status
string status = manager.GetImmobilizationStatus();
```

### Individual Player Immobilization Control
```csharp
// Get player's immobilizer component
HelicopterPlayerImmobilizer immobilizer = player.GetComponent<HelicopterPlayerImmobilizer>();

// Check if immobilization is active
bool isImmobilized = immobilizer.ImmobilizationActive;

// Check if movement input should be blocked
bool blockMovement = immobilizer.ShouldBlockMovementInput();

// Check if camera input should be blocked
bool blockCamera = immobilizer.ShouldBlockCameraInput();
```

## System Behavior

### Immobilization Effects
When active, the system:
1. **Disables Movement Components**: Movement.cs, ForestPlayer movement
2. **Disables Physics**: CharacterController, NavMeshAgent, Rigidbody gravity
3. **Blocks Input**: Movement input (and optionally camera input)
4. **Enforces Position**: Continuously locks player to spawn point local position (0,0,0)
5. **Preserves Rotation**: Optionally locks rotation to initial state

### Component State Management
The system carefully tracks and restores component states:
- Saves original enabled/disabled states before immobilization
- Restores exact original states when immobilization ends
- Handles edge cases where components are modified during immobilization

### Network Synchronization
- Global immobilization state is synchronized across all clients
- Individual player immobilization states are network-synchronized
- Server authoritative control ensures consistent behavior

## Debugging and Visualization

### Debug Logging
Enable debug logs in both manager and individual immobilizer components for detailed operation tracking.

### Visual Debugging
The HelicopterPlayerImmobilizer component provides Gizmos when selected:
- **Yellow/Red Sphere**: Shows locked position (yellow when inactive, red when active)
- **Cyan Line**: Shows distance from current position to locked position

### Status Checking
```csharp
// Get comprehensive status information
string status = HelicopterImmobilizationManager.Instance.GetImmobilizationStatus();
Debug.Log(status);
// Output: "Global: True, Registered Players: 4, Helicopter State: MovingToWaypoint"
```

## Troubleshooting

### Common Issues

**Players not immobilized during helicopter sequence:**
1. Check that HelicopterImmobilizationManager is in the scene
2. Verify ForestGameManager has reference to immobilization manager
3. Ensure players have HelicopterPlayerImmobilizer components

**Players stuck after helicopter lands:**
1. Check helicopter state machine is transitioning to Landing/PathComplete
2. Verify exit sequence is properly triggering
3. Check component state restoration in debug logs

**Movement still working during immobilization:**
1. Verify movement components are checking immobilization state
2. Check that ShouldBlockMovementInput() returns true
3. Ensure immobilizer component is properly added to players

### Debug Commands
```csharp
// Force deactivate if players are stuck
HelicopterImmobilizationManager.Instance.ForceDeactivateImmobilization();

// Check individual player state
var immobilizer = player.GetComponent<HelicopterPlayerImmobilizer>();
Debug.Log($"Immobilized: {immobilizer.ImmobilizationActive}, Valid Spawn Point: {immobilizer.HasValidSpawnPoint}");
```
