using Mirror;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LobbyController : MonoBehaviour
{
    public static LobbyController instance;

    [Header("Transition Settings")]
    [SerializeField] private bool useEnhancedTransitionSystem = true;
    [SerializeField] private float transitionRetryDelay = 3f;
    [SerializeField] private int maxTransitionRetries = 3;

    private int currentTransitionAttempts = 0;
    private bool isTransitionInProgress = false;

    private void Awake()
    {
        instance = this;
    }

    private void Start()
    {
        // Subscribe to transition events if using enhanced system
        if (useEnhancedTransitionSystem && SceneTransitionManager.Instance != null)
        {
            SceneTransitionManager.OnTransitionStateChangedEvent += OnTransitionStateChanged;
            SceneTransitionManager.OnTransitionError += OnTransitionError;
        }
    }

    private void OnDestroy()
    {
        // Unsubscribe from transition events
        if (SceneTransitionManager.Instance != null)
        {
            SceneTransitionManager.OnTransitionStateChangedEvent -= OnTransitionStateChanged;
            SceneTransitionManager.OnTransitionError -= OnTransitionError;
        }
    }

    public void StartGameWithParty()
    {
        if (!NetworkServer.active)
        {
            Debug.LogWarning("LobbyController: Cannot start game - server is not active");
            return;
        }

        if (isTransitionInProgress)
        {
            Debug.LogWarning("LobbyController: Transition already in progress");
            return;
        }

        if (!AllPlayersReady())
        {
            Debug.LogWarning("LobbyController: Cannot start game - not all players are ready");
            return;
        }

        StartGame();
    }

    public void StartGameSolo()
    {
        if (isTransitionInProgress)
        {
            Debug.LogWarning("LobbyController: Transition already in progress");
            return;
        }

        StartCoroutine(StartSinglePlayer());
    }

    IEnumerator StartSinglePlayer()
    {
        isTransitionInProgress = true;

        try
        {
            NetworkManager.singleton.StartHost();

            while(NetworkClient.localPlayer == null)
                yield return new WaitForEndOfFrame();

            ((MyNetworkManager)NetworkManager.singleton).SetMultiplayer(false);
            StartGame();
        }
        catch (Exception ex)
        {
            Debug.LogError($"LobbyController: Error starting single player: {ex.Message}");
            isTransitionInProgress = false;
        }
    }

    private void StartGame()
    {
        if (!NetworkServer.active)
        {
            Debug.LogError("LobbyController: Cannot start game - server is not active");
            return;
        }

        isTransitionInProgress = true;
        currentTransitionAttempts++;

        if (useEnhancedTransitionSystem && SceneTransitionManager.Instance != null)
        {
            // Use enhanced transition system
            bool transitionStarted = SceneTransitionManager.Instance.StartTransitionToMap01();
            if (!transitionStarted)
            {
                Debug.LogError("LobbyController: Failed to start enhanced transition");
                HandleTransitionFailure();
            }
        }
        else
        {
            // Use legacy transition system as fallback
            StartLegacyTransition();
        }
    }

    private void StartLegacyTransition()
    {
        Debug.LogWarning("LobbyController: Using legacy transition system");

        try
        {
            // Clean up lobby state before scene transition
            if (LobbyPlayerList.instance != null)
            {
                // Notify all clients that game is starting
                foreach (var client in LobbyPlayerList.instance.allClients)
                {
                    if (client != null)
                    {
                        client.IsReady = false; // Reset ready state for new scene
                    }
                }
            }

            if (MainMenu.instance != null)
            {
                Destroy(MainMenu.instance.gameObject);
            }

            NetworkManager.singleton.ServerChangeScene("Map_01");

            // Start a coroutine to monitor legacy transition
            StartCoroutine(MonitorLegacyTransition());
        }
        catch (Exception ex)
        {
            Debug.LogError($"LobbyController: Legacy transition failed: {ex.Message}");
            HandleTransitionFailure();
        }
    }

    private IEnumerator MonitorLegacyTransition()
    {
        float startTime = Time.time;
        float timeout = 30f; // 30 second timeout

        while (Time.time - startTime < timeout)
        {
            // Check if scene has loaded
            if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "Map_01")
            {
                Debug.Log("LobbyController: Legacy transition completed successfully");
                isTransitionInProgress = false;
                currentTransitionAttempts = 0;
                yield break;
            }

            yield return new WaitForSeconds(0.5f);
        }

        // Timeout reached
        Debug.LogError("LobbyController: Legacy transition timed out");
        HandleTransitionFailure();
    }

    public bool AllPlayersReady()
    {
        if (LobbyPlayerList.instance == null)
        {
            Debug.LogError("LobbyController: LobbyPlayerList.instance is null. Cannot check if all players are ready.");
            return false;
        }

        if (LobbyPlayerList.instance.allClients.Count == 0)
        {
            Debug.LogWarning("LobbyController: No clients in lobby");
            return false;
        }

        foreach (MyClient client in LobbyPlayerList.instance.allClients)
        {
            if (client == null || !client.IsReady)
            {
                return false;
            }
        }
        return true;
    }

    private void OnTransitionStateChanged(SceneTransitionManager.TransitionState newState)
    {
        Debug.Log($"LobbyController: Transition state changed to {newState}");

        switch (newState)
        {
            case SceneTransitionManager.TransitionState.Complete:
                isTransitionInProgress = false;
                currentTransitionAttempts = 0;
                Debug.Log("LobbyController: Transition completed successfully");
                break;

            case SceneTransitionManager.TransitionState.Failed:
                HandleTransitionFailure();
                break;
        }
    }

    private void OnTransitionError(string error)
    {
        Debug.LogError($"LobbyController: Transition error - {error}");
        HandleTransitionFailure();
    }

    private void HandleTransitionFailure()
    {
        isTransitionInProgress = false;

        if (currentTransitionAttempts < maxTransitionRetries)
        {
            Debug.LogWarning($"LobbyController: Retrying transition (attempt {currentTransitionAttempts + 1}/{maxTransitionRetries})");
            StartCoroutine(RetryTransitionAfterDelay());
        }
        else
        {
            Debug.LogError("LobbyController: Maximum transition retries exceeded. Transition failed permanently.");
            currentTransitionAttempts = 0;

            // Notify UI or take other recovery actions
            NotifyTransitionFailure();
        }
    }

    private IEnumerator RetryTransitionAfterDelay()
    {
        yield return new WaitForSeconds(transitionRetryDelay);

        // Verify conditions are still valid for retry
        if (NetworkServer.active && AllPlayersReady())
        {
            Debug.Log("LobbyController: Retrying transition");
            StartGame();
        }
        else
        {
            Debug.LogWarning("LobbyController: Cannot retry transition - conditions no longer valid");
            currentTransitionAttempts = 0;
        }
    }

    private void NotifyTransitionFailure()
    {
        // This could trigger UI notifications, return to main menu, etc.
        Debug.LogError("LobbyController: Transition failed permanently. Manual intervention required.");

        // Reset lobby state
        if (LobbyPlayerList.instance != null)
        {
            foreach (var client in LobbyPlayerList.instance.allClients)
            {
                if (client != null)
                {
                    client.IsReady = false;
                }
            }
        }
    }

    /// <summary>
    /// Get current transition status for UI display
    /// </summary>
    public bool IsTransitionInProgress()
    {
        return isTransitionInProgress;
    }

    /// <summary>
    /// Get current transition attempt count
    /// </summary>
    public int GetTransitionAttempts()
    {
        return currentTransitionAttempts;
    }
}
