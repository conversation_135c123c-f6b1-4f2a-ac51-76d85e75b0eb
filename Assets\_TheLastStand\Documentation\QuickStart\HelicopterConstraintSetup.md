# Helicopter Constraint System - Quick Setup Guide

## Automatic Setup (Recommended)

The helicopter constraint system is designed to work automatically with minimal setup required.

### Prerequisites
1. Ensure your scene has a `ForestIntroHelicopter` component
2. Ensure your scene has a `HelicopterExitManager` component
3. Ensure your scene has a `HelicopterImmobilizationManager` component (for complete immobilization)
4. Players should be spawned using the `ForestPlayerManager` system

### Automatic Integration
The system automatically:
- Adds `HelicopterTransformConstraint` components to networked players during spawning
- Activates constraints when helicopter begins flight
- Deactivates constraints when helicopter lands or exit sequence begins
- Synchronizes constraint states across all networked clients

## Manual Setup (If Needed)

### 1. Add HelicopterConstraintManager to Scene
```csharp
// Create empty GameObject in scene
GameObject constraintManagerGO = new GameObject("HelicopterConstraintManager");
HelicopterConstraintManager manager = constraintManagerGO.AddComponent<HelicopterConstraintManager>();

// Assign references (optional - auto-detection will find them)
manager.helicopter = FindObjectOfType<ForestIntroHelicopter>();
manager.exitManager = FindObjectOfType<HelicopterExitManager>();
```

### 2. Add Constraints to Existing Players (If Needed)
```csharp
// For players already in scene without constraints
ForestPlayer[] players = FindObjectsOfType<ForestPlayer>();
foreach (ForestPlayer player in players)
{
    if (player.GetComponent<HelicopterTransformConstraint>() == null)
    {
        player.gameObject.AddComponent<HelicopterTransformConstraint>();
    }
}
```

## Configuration

### Default Constraint Settings
The system comes with sensible defaults:
- **Position:** Locked to (0,0,0) relative to spawn point
- **X Rotation:** Locked (no pitch)
- **Z Rotation:** Locked (no roll)
- **Y Rotation:** ±90° range (allows looking left/right at other players)

### Customizing Constraints
Modify the `HelicopterTransformConstraint` component on individual players:

```csharp
HelicopterTransformConstraint constraint = player.GetComponent<HelicopterTransformConstraint>();

// Adjust Y rotation limits for wider/narrower social interaction range
constraint.yRotationMin = -120f;  // Look further left
constraint.yRotationMax = 120f;   // Look further right

// Adjust constraint smoothness (higher = smoother but slower correction)
constraint.constraintSmoothness = 15f;

// Enable visual feedback for debugging
constraint.enableVisualFeedback = true;
constraint.showDebugInfo = true;
```

## Testing the System

### 1. Verify Automatic Setup
1. Start multiplayer session with helicopter scene
2. Check console for constraint system initialization messages
3. Verify players spawn with constraint components attached

### 2. Test Constraint Activation
1. Start helicopter flight sequence
2. Observe constraint activation messages in console
3. Test player rotation limits during flight
4. Verify position locking to spawn points

### 3. Test Constraint Deactivation
1. Let helicopter complete landing sequence
2. Observe constraint deactivation messages
3. Verify players can move freely after exit sequence

## Troubleshooting

### Common Issues

**Constraints not activating:**
- Check that `HelicopterConstraintManager` exists in scene
- Verify helicopter reference is assigned or auto-detected
- Ensure helicopter state changes are being monitored

**Players can rotate beyond limits:**
- Check that `HelicopterTransformConstraint` component exists on player
- Verify constraint activation state with debug logging
- Ensure player scripts are calling constraint checking methods

**Network synchronization issues:**
- Verify Mirror networking is properly configured
- Check that constraint state changes are happening on server
- Ensure `[SyncVar]` hooks are functioning correctly

### Debug Commands

Enable debug logging in `HelicopterConstraintManager`:
```csharp
manager.showDebugLogs = true;
```

Enable debug info in individual constraints:
```csharp
constraint.showDebugInfo = true;
```

Check system status at runtime:
```csharp
string status = HelicopterConstraintManager.Instance.GetConstraintStatus();
Debug.Log(status);
```

## Performance Notes

- Constraints only enforce on local players (`isLocalPlayer`)
- Constraint checking runs in `Update()` only when constraints are active
- Network synchronization uses efficient `[SyncVar]` system
- Visual gizmos only render in Scene view during development

## Integration with Existing Systems

The constraint system integrates seamlessly with:
- **ForestPlayerManager:** Automatic constraint component addition
- **ForestIntroPlayer/ForestPlayer:** Mouse look constraint checking
- **HelicopterExitManager:** Automatic constraint deactivation
- **Helicopter State Machine:** Automatic activation based on flight state
- **HelicopterImmobilizationManager:** Works alongside for complete player immobilization

**Note:** For complete player immobilization (including movement and physics disabling), use the [Helicopter Immobilization System](./HelicopterImmobilizationSetup.md) instead of or alongside this constraint system.
