# Player Management - TheLastStand

## 🎮 Overview

The player management system handles player spawning, lifecycle, and scene-specific behavior. It coordinates between network requirements and game-specific functionality through a centralized spawning architecture.

## 🏗️ Player Architecture

```mermaid
graph TB
    subgraph "Network Layer"
        MyNetworkManager[MyNetworkManager]
        NetworkConnection[Network Connection]
    end
    
    subgraph "Player Components"
        MyClient[MyClient]
        ForestIntroPlayer[ForestIntroPlayer]
        HelicopterPlayerImmobilizer[Helicopter Player Immobilizer]
        CharacterSkin[Character Skin]
        PlayerIdentity[Player Identity]
    end
    
    subgraph "Scene Management"
        ForestPlayerManager[Forest Player Manager]
        CharacterSkinHandler[Character Skin Handler]
    end
    
    MyNetworkManager --> ForestPlayerManager
    ForestPlayerManager --> MyClient
    MyClient --> ForestIntroPlayer
    MyClient --> CharacterSkin
    CharacterSkinHandler --> CharacterSkin
```

## 🔧 Core Components

### MyClient (Network Player)
**Location:** `Assets/_TheLastStand/Scripts/Player/MyClient.cs`

The primary networked player component that represents a player across the network.

**Key Features:**
- Steam profile integration
- Ready state management
- Scene-specific behavior adaptation
- Character appearance handling

**SyncVars:**
```csharp
[SyncVar(hook = nameof(PlayerInfoUpdate))]
public PlayerInfoData playerInfo;

[SyncVar(hook = nameof(IsReadyUpdate))]
public bool IsReady;

[SyncVar(hook = nameof(OnAvatarDataChanged))]
public byte[] avatarData;
```

**Public Properties:**
```csharp
public GameObject MeshObj { get; }      // Player visual mesh
public GameObject CamHolder { get; }    // Camera container
public Sprite icon { get; }             // Steam avatar
public CharacterSkinElement characterInstance { get; set; }
```

### ForestIntroPlayer (Scene-Specific Controller)
**Location:** `Assets/_TheLastStand/Scripts/ForestMap/ForestIntroPlayer.cs`

Handles player control and camera management during the forest intro sequence.

**Key Features:**
- Mouse look controls
- Camera management
- Settings panel integration
- Player model rotation
- Helicopter immobilization integration

**Configuration:**
```csharp
[SerializeField] private float mouseSensitivity = 200f;
[SerializeField] private float verticalLookMinAngle = -85f;
[SerializeField] private float verticalLookMaxAngle = 85f;
```

## 🚀 Spawning System

### Centralized Spawning Architecture

The spawning system eliminates duplicate player creation through scene-aware coordination:

```mermaid
sequenceDiagram
    participant Client
    participant MyNetworkManager
    participant ForestPlayerManager
    participant MyClient
    participant ForestIntroPlayer
    
    Client->>MyNetworkManager: OnServerAddPlayer()
    MyNetworkManager->>MyNetworkManager: Check scene name
    
    alt Map_01 Scene
        MyNetworkManager->>ForestPlayerManager: SpawnNetworkedPlayer()
        ForestPlayerManager->>MyClient: Instantiate at spawn point
        ForestPlayerManager->>ForestIntroPlayer: Add component
        ForestPlayerManager->>HelicopterPlayerImmobilizer: Add component
        ForestPlayerManager->>ForestIntroPlayer: Configure camera
        ForestPlayerManager->>MyNetworkManager: InitializeSpawnedPlayer()
    else Other Scenes
        MyNetworkManager->>MyClient: Default spawning
    end
```

### ForestPlayerManager
**Location:** `Assets/_TheLastStand/Scripts/ForestMap/ForestPlayerManager.cs`

Coordinates player spawning in the forest scene with proper intro sequence integration.

**Key Methods:**
```csharp
public void SpawnNetworkedPlayer(NetworkConnectionToClient conn, GameObject networkPlayerPrefab)
private Transform GetSpawnPointForConnection(NetworkConnectionToClient conn)
private void ConfigureNetworkedPlayerForIntro(GameObject networkedPlayer, NetworkConnectionToClient conn)
```

**Spawn Point Assignment:**
- **Host (Local Connection)** → `localPlayerSpawnPoint`
- **Remote Clients** → `replicatedPlayerSpawnPoints[connectionIndex]`
- **Fallback** → `localPlayerSpawnPoint` if no specific point available

## 🎯 Player Lifecycle

### 1. **Connection & Spawning**
```mermaid
stateDiagram-v2
    [*] --> Connecting
    Connecting --> Spawning: Connection established
    Spawning --> Initializing: Player instantiated
    Initializing --> Ready: Components configured
    Ready --> Playing: Scene loaded
    Playing --> Disconnecting: Player leaves
    Disconnecting --> [*]: Cleanup complete
```

### 2. **Component Integration**
When a player spawns in Map_01:

1. **MyClient** instantiated at spawn point
2. **ForestIntroPlayer** component added dynamically
3. **HelicopterPlayerImmobilizer** component added for helicopter sequences
4. **Camera** configured for local player
5. **Transform** reset to spawn point local coordinates
6. **Steam data** synchronized across network

### 3. **Scene Transitions**
Players maintain their network identity across scene changes:

```csharp
// In MyClient.Start()
string currentScene = SceneManager.GetActiveScene().name;

if (currentScene != "MainMenu")
{
    ToggleController(true);
    
    if (currentScene == "Map_01")
    {
        HandleForestIntroSetup();
    }
}
```

## 👤 Character System

### CharacterSkinHandler
**Location:** `Assets/_TheLastStand/Scripts/Player/CharacterSkinHandler.cs`

Manages character appearance and visual representation.

**Key Features:**
- Platform-based character assignment
- Local vs remote player differentiation
- Character mesh spawning and cleanup

**Character Assignment Logic:**
```csharp
public void SpawnCharacterMesh(MyClient client) 
{
    int index = GetNextPlatformIndex(client);

    if(client && client.isLocalPlayer)
    {
        client.characterInstance = clientsCharacters[0];
        return;
    }

    clientsCharacters[index] = Instantiate(characterSkinPrefab, spawnPositions[index]);
    client.characterInstance = clientsCharacters[index];
}
```

### Steam Profile Integration

Players display Steam profile information:

**Avatar Handling:**
```csharp
private void OnAvatarDataChanged(byte[] oldData, byte[] newData)
{
    if (newData != null && newData.Length > 0)
    {
        Texture2D tex = new Texture2D(2, 2);
        if (tex.LoadImage(newData))
        {
            icon = SteamHelper.ConvertTextureToSprite(tex);
        }
    }
    OnClientUpdated?.Invoke();
}
```

## 🎮 Player Controls

### Forest Intro Controls
During the forest intro sequence:

**Mouse Look:**
- **Horizontal** → Player rotation (Y-axis)
- **Vertical** → Camera rotation (X-axis)
- **Sensitivity** → Configurable via `mouseSensitivity`

**Helicopter Immobilization:**
- **Movement Input** → Completely blocked during helicopter flight
- **Camera Input** → Optionally blocked (configurable)
- **Position Locking** → Enforced to spawn point local coordinates (0,0,0)
- **Physics** → Disabled (gravity, CharacterController, NavMeshAgent)

**Input Handling:**
```csharp
private void HandleMouseLook()
{
    float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity * Time.deltaTime;
    float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity * Time.deltaTime;

    transform.Rotate(Vector3.up * mouseX);
    
    if (cameraTransform != null)
    {
        _currentCameraXRotation -= mouseY;
        _currentCameraXRotation = Mathf.Clamp(_currentCameraXRotation, verticalLookMinAngle, verticalLookMaxAngle);
        cameraTransform.localRotation = Quaternion.Euler(_currentCameraXRotation, 0f, 0f);
    }
}
```

## 🔧 Configuration

### Spawn Points Setup
In the Map_01 scene, configure spawn points:

```csharp
[Header("Spawn Points")]
[SerializeField] private Transform localPlayerSpawnPoint;
[SerializeField] private List<Transform> replicatedPlayerSpawnPoints;
```

**Best Practices:**
- Position spawn points to avoid overlapping
- Ensure spawn points are on NavMesh for AI compatibility
- Set appropriate rotations for player facing direction
- Test with maximum player count (4 players)

### Camera Configuration
```csharp
private readonly Vector3 _cameraLocalOffset = new Vector3(0f, 1.693f, 0.174f);
private readonly Quaternion _cameraLocalRotation = Quaternion.identity;
```

## 🐛 Common Issues

### Spawning Problems
- **Duplicate Players** → Check ForestPlayerManager coordination
- **Missing Camera** → Verify camera assignment in ForestPlayerManager
- **Wrong Spawn Position** → Check spawn point configuration

### Component Issues
- **Missing ForestIntroPlayer** → Ensure dynamic component addition works
- **Steam Data Not Syncing** → Check SyncVar hooks
- **Character Skin Problems** → Verify CharacterSkinHandler setup

### Performance Issues
- **Too Many SyncVar Updates** → Optimize change detection
- **Camera Jitter** → Check mouse sensitivity settings
- **Network Lag** → Monitor NetworkPerformanceMonitor

---

**Next:** [Scene Management](./SceneManagement.md) | [Steam Integration](../GameSystems/SteamIntegration.md)
