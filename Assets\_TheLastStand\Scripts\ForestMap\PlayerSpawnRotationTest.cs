using UnityEngine;
using System.Collections;

/// <summary>
/// Test script to verify player spawning rotation fixes
/// Attach to ForestPlayerManager to run automated rotation tests
/// </summary>
public class PlayerSpawnRotationTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool runTestsOnStart = false;
    [SerializeField] private bool enableDebugLogs = true;
    
    private ForestPlayerManager playerManager;
    
    void Start()
    {
        playerManager = GetComponent<ForestPlayerManager>();
        
        if (runTestsOnStart && playerManager != null)
        {
            StartCoroutine(RunRotationTests());
        }
    }
    
    private IEnumerator RunRotationTests()
    {
        yield return new WaitForSeconds(2f); // Wait for initial spawning to complete
        
        LogTest("=== PLAYER SPAWN ROTATION VERIFICATION TESTS ===");
        
        // Test 1: Verify local player rotation
        yield return StartCoroutine(TestLocalPlayerRotation());
        
        // Test 2: Verify replicated player rotations
        yield return StartCoroutine(TestReplicatedPlayerRotations());
        
        // Test 3: Verify networked player rotation (if in multiplayer)
        if (MyNetworkManager.isMultiplayer)
        {
            yield return StartCoroutine(TestNetworkedPlayerRotation());
        }
        
        LogTest("=== ROTATION TESTS COMPLETED ===");
    }
    
    private IEnumerator TestLocalPlayerRotation()
    {
        LogTest("--- Testing Local Player Rotation ---");

        ForestIntroPlayer localPlayer = playerManager.GetLocalPlayerInstance();
        if (localPlayer != null)
        {
            Vector3 initialRotation = localPlayer.transform.rotation.eulerAngles;
            Vector3 localPosition = localPlayer.transform.localPosition;
            LogTest($"Local player world rotation: {initialRotation}");
            LogTest($"Local player local position: {localPosition}");
            LogTest($"Local player world position: {localPlayer.transform.position}");

            // Check if player is upright (X and Z should be close to 0)
            bool isUpright = Mathf.Abs(initialRotation.x) < 5f && Mathf.Abs(initialRotation.z) < 5f;
            LogTest($"Local player upright check: {(isUpright ? "PASS" : "FAIL")} (X: {initialRotation.x:F2}, Z: {initialRotation.z:F2})");

            // Check if local position is near zero (should match replicated players)
            bool positionCorrect = localPosition.magnitude < 0.1f;
            LogTest($"Local player position check: {(positionCorrect ? "PASS" : "FAIL")} (magnitude: {localPosition.magnitude:F6})");

            // Test player model rotation if available
            MyClient myClient = localPlayer.GetComponent<MyClient>();
            if (myClient != null && myClient.MeshObj != null)
            {
                Vector3 modelRotation = myClient.MeshObj.transform.rotation.eulerAngles;
                LogTest($"Local player model rotation: {modelRotation}");

                bool modelUpright = Mathf.Abs(modelRotation.x) < 5f && Mathf.Abs(modelRotation.z) < 5f;
                LogTest($"Local player model upright check: {(modelUpright ? "PASS" : "FAIL")} (X: {modelRotation.x:F2}, Z: {modelRotation.z:F2})");
            }

            // Check parent hierarchy
            if (localPlayer.transform.parent != null)
            {
                LogTest($"Local player parent: {localPlayer.transform.parent.name}");
                LogTest($"Local player parent world position: {localPlayer.transform.parent.position}");
                LogTest($"Local player parent world rotation: {localPlayer.transform.parent.rotation.eulerAngles}");
            }
            else
            {
                LogTest("Local player has no parent - UNEXPECTED");
            }

            // Check helicopter constraint status
            HelicopterTransformConstraint constraint = localPlayer.GetComponent<HelicopterTransformConstraint>();
            if (constraint != null)
            {
                LogTest($"Local player helicopter constraint found: Active={constraint.ConstraintsActive}, HasValidSpawnPoint={constraint.HasValidSpawnPoint}");
            }
            else
            {
                LogTest("Local player helicopter constraint NOT FOUND - ISSUE");
            }
        }
        else
        {
            LogTest("Local player not found - SKIP");
        }

        yield return null;
    }
    
    private IEnumerator TestReplicatedPlayerRotations()
    {
        LogTest("--- Testing Replicated Player Rotations ---");

        ForestIntroReplicatedPlayer[] replicatedPlayers = FindObjectsOfType<ForestIntroReplicatedPlayer>();

        if (replicatedPlayers.Length > 0)
        {
            for (int i = 0; i < replicatedPlayers.Length; i++)
            {
                ForestIntroReplicatedPlayer player = replicatedPlayers[i];
                Vector3 playerRotation = player.transform.rotation.eulerAngles;
                Vector3 localPosition = player.transform.localPosition;
                LogTest($"Replicated player {i} world rotation: {playerRotation}");
                LogTest($"Replicated player {i} local position: {localPosition}");
                LogTest($"Replicated player {i} world position: {player.transform.position}");

                bool isUpright = Mathf.Abs(playerRotation.x) < 5f && Mathf.Abs(playerRotation.z) < 5f;
                LogTest($"Replicated player {i} upright check: {(isUpright ? "PASS" : "FAIL")} (X: {playerRotation.x:F2}, Z: {playerRotation.z:F2})");

                // Check if local position is near zero
                bool positionCorrect = localPosition.magnitude < 0.1f;
                LogTest($"Replicated player {i} position check: {(positionCorrect ? "PASS" : "FAIL")} (magnitude: {localPosition.magnitude:F6})");

                // Check parent hierarchy
                if (player.transform.parent != null)
                {
                    LogTest($"Replicated player {i} parent: {player.transform.parent.name}");
                }
                else
                {
                    LogTest($"Replicated player {i} has no parent - UNEXPECTED");
                }
            }
        }
        else
        {
            LogTest("No replicated players found - SKIP");
        }

        yield return null;
    }
    
    private IEnumerator TestNetworkedPlayerRotation()
    {
        LogTest("--- Testing Networked Player Rotations ---");
        
        MyClient[] networkedClients = FindObjectsOfType<MyClient>();
        
        if (networkedClients.Length > 0)
        {
            for (int i = 0; i < networkedClients.Length; i++)
            {
                MyClient client = networkedClients[i];
                Vector3 clientRotation = client.transform.rotation.eulerAngles;
                LogTest($"Networked client {i} rotation: {clientRotation}");
                
                bool isUpright = Mathf.Abs(clientRotation.x) < 5f && Mathf.Abs(clientRotation.z) < 5f;
                LogTest($"Networked client {i} upright check: {(isUpright ? "PASS" : "FAIL")} (X: {clientRotation.x:F2}, Z: {clientRotation.z:F2})");
                
                // Test mesh object rotation
                if (client.MeshObj != null)
                {
                    Vector3 meshRotation = client.MeshObj.transform.rotation.eulerAngles;
                    LogTest($"Networked client {i} mesh rotation: {meshRotation}");
                    
                    bool meshUpright = Mathf.Abs(meshRotation.x) < 5f && Mathf.Abs(meshRotation.z) < 5f;
                    LogTest($"Networked client {i} mesh upright check: {(meshUpright ? "PASS" : "FAIL")} (X: {meshRotation.x:F2}, Z: {meshRotation.z:F2})");
                }
            }
        }
        else
        {
            LogTest("No networked clients found - SKIP");
        }
        
        yield return null;
    }
    
    private void LogTest(string message)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[PlayerSpawnRotationTest] {message}");
        }
    }
    
    /// <summary>
    /// Manual test trigger for runtime testing
    /// </summary>
    [ContextMenu("Run Rotation Tests")]
    public void RunTestsManually()
    {
        if (playerManager == null)
            playerManager = GetComponent<ForestPlayerManager>();

        StartCoroutine(RunRotationTests());
    }

    /// <summary>
    /// Quick verification of local player transform values
    /// </summary>
    [ContextMenu("Quick Local Player Check")]
    public void QuickLocalPlayerCheck()
    {
        if (playerManager == null)
            playerManager = GetComponent<ForestPlayerManager>();

        ForestIntroPlayer localPlayer = playerManager.GetLocalPlayerInstance();
        if (localPlayer != null)
        {
            Debug.Log($"[QUICK CHECK] Local player position: {localPlayer.transform.position}");
            Debug.Log($"[QUICK CHECK] Local player rotation: {localPlayer.transform.rotation.eulerAngles}");
            Debug.Log($"[QUICK CHECK] Local player local position: {localPlayer.transform.localPosition}");
            Debug.Log($"[QUICK CHECK] Local player local rotation: {localPlayer.transform.localRotation.eulerAngles}");
            Debug.Log($"[QUICK CHECK] Local player parent: {(localPlayer.transform.parent != null ? localPlayer.transform.parent.name : "None")}");
        }
        else
        {
            Debug.Log("[QUICK CHECK] Local player not found");
        }
    }
}
